# Schedule页面标签栏响应式布局修复文档

## 1. 问题描述

在小屏幕设备（特别是宽度为390px及以下）上查看`schedule`（课程表）页面时，顶部`t-tabs`组件的激活指示器（底部的蓝色线条）会消失或被部分裁切。

- **正常设备**: 屏幕尺寸 `430×932px`
- **问题设备**: 屏幕尺寸 `390×844px` 及更小

## 2. 问题根源分析

经过代码分析，定位到问题主要出在`schedule.wxss`文件中定义的自定义标签栏样式。

1.  **容器空间不足**: 包裹`t-tabs`组件的父容器`.top-tabs-section`的`padding-bottom`为`0`。
2.  **指示器实现方式**: 激活的标签页指示器是通过`.t-tabs__item--active`的`border-bottom: 3px solid #0052d9;`实现的。
3.  **高度压缩**: 在小屏幕上，由于空间有限，`t-tabs`组件的整体高度被压缩。
4.  **裁切现象**: 当容器高度被压缩时，没有足够底部内边距的`.top-tabs-section`容器就会将作为`border`的指示器裁切掉，导致其不可见。

## 3. 解决方案

为了解决这个问题，我们对`miniprogram/pages/schedule/schedule.wxss`文件进行了以下三处关键修改：

### 3.1. 为指示器预留空间

我们为`.top-tabs-section`容器增加了一个`4px`的底部内边距，确保即使在高度被压缩时，也有足够的空间来显示`3px`的`border-bottom`指示器。

**原始代码**:
```css
.top-tabs-section {
  /* ... */
  padding: 0px 16px 0 16px;
  /* ... */
}
```

**修复后代码**:
```css
.top-tabs-section {
  /* ... */
  /* 增加4px底部内边距，为激活指示器预留空间 */
  padding: 0 16px 4px;
  /* ... */
}
```

### 3.2. 增加390px断点的媒体查询

为了更精确地适配iPhone 12/13 Pro等常见的小尺寸屏幕，我们新增了一个`max-width: 390px`的媒体查询断点。在此断点下，我们微调了标签项的内边距、字体大小和最小高度，使其布局更加紧凑和健壮。

### 3.3. 优化375px断点下的样式

我们进一步优化了在`max-width: 375px`（如iPhone 6/7/8/SE）下的样式，进一步减小了标签项的水平内边距，防止内容溢出。

**修复后的响应式代码块**:
```css
/* 响应式布局调整 */
/* 针对小屏幕进行优化，确保标签栏在不同尺寸下都能正确显示 */
@media (max-width: 390px) {
  .custom-top-tabs .t-tabs__item {
    padding: 12px 14px !important; /* 减小水平内边距 */
    font-size: 15px !important; /* 缩小字体 */
    min-height: 42px; /* 减小最小高度 */
  }
}

@media (max-width: 375px) {
  .container {
    padding: 8px;
    padding-bottom: calc(8px + 120rpx + env(safe-area-inset-bottom));
  }

  .course-card {
    padding: 12px;
  }

  .course-title {
    font-size: 17px;
  }

  .info-item {
    font-size: 13px;
  }

  .custom-top-tabs .t-tabs__item {
    padding: 12px 12px !important; /* 进一步减小内边距 */
    font-size: 15px !important;
  }
}
```

## 4. 结论

通过以上修复，`schedule`页面的顶部标签栏现在可以在所有目标屏幕尺寸上正确、稳定地显示其激活指示器，解决了响应式布局问题。
