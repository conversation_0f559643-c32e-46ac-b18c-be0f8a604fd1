// membershipCardUtils.js
// 会员卡管理工具函数
// 提供多卡支持和智能扣减策略的核心功能

/**
 * 会员卡状态枚举
 */
export const CARD_STATUS = {
  NORMAL: '正常',      // 正常可用
  EXPIRED: '已过期',   // 已过期
  EXHAUSTED: '已用完', // 次数用完
  FROZEN: '已冻结'     // 已冻结
};

/**
 * 获取用户的所有有效会员卡（按智能扣减优先级排序）
 * 
 * 排序规则：
 * 1. 优先扣减到期时间最近的卡片
 * 2. 如果到期时间相同，按创建时间先后顺序（先创建的先扣减）
 * 3. 跳过已过期或次数为0的卡片
 * 
 * @param {string} userId - 用户openid
 * @param {Object} db - 数据库实例
 * @returns {Promise<Array>} 排序后的有效会员卡列表
 */
export async function getValidMembershipCards(userId, db) {
  try {
    const now = new Date();
    
    // 查询用户的所有会员卡
    const result = await db.collection('membershipCard')
      .where({
        userId: userId,
        status: CARD_STATUS.NORMAL,
        validFrom: db.command.lte(now),
        validTo: db.command.gte(now),
        remainingTimes: db.command.gt(0)
      })
      .get();
    
    // 按智能扣减策略排序
    const sortedCards = result.data.sort((a, b) => {
      // 首先按到期时间升序排序（越早到期的越优先）
      const timeA = new Date(a.validTo).getTime();
      const timeB = new Date(b.validTo).getTime();
      
      if (timeA !== timeB) {
        return timeA - timeB;
      }
      
      // 如果到期时间相同，按创建时间升序排序（先创建的先扣减）
      const createTimeA = new Date(a.createTime).getTime();
      const createTimeB = new Date(b.createTime).getTime();
      
      return createTimeA - createTimeB;
    });
    
    return sortedCards;
  } catch (error) {
    console.error('获取有效会员卡失败:', error);
    throw error;
  }
}

/**
 * 获取用户的所有会员卡（包括无效的，用于展示）
 * 
 * 排序规则：
 * 1. 有效卡片在前，无效卡片在后
 * 2. 有效卡片按到期时间升序排序
 * 3. 无效卡片按创建时间降序排序
 * 
 * @param {string} userId - 用户openid
 * @param {Object} db - 数据库实例
 * @returns {Promise<Array>} 排序后的所有会员卡列表
 */
export async function getAllMembershipCards(userId, db) {
  try {
    const result = await db.collection('membershipCard')
      .where({
        userId: userId
      })
      .get();
    
    const now = new Date();
    
    // 计算每张卡的状态和优先级
    const cardsWithStatus = result.data.map(card => {
      const validTo = new Date(card.validTo);
      const validFrom = new Date(card.validFrom);
      
      let cardStatus = card.status;
      let isValid = false;
      let isExpiring = false;
      
      // 重新计算状态
      if (card.status === CARD_STATUS.NORMAL) {
        if (now > validTo) {
          cardStatus = CARD_STATUS.EXPIRED;
        } else if (card.remainingTimes <= 0) {
          cardStatus = CARD_STATUS.EXHAUSTED;
        } else if (now >= validFrom && now <= validTo) {
          isValid = true;
          // 检查是否即将到期（7天内）
          isExpiring = (validTo - now) < 7 * 24 * 60 * 60 * 1000;
        }
      }
      
      return {
        ...card,
        computedStatus: cardStatus,
        isValid: isValid,
        isExpiring: isExpiring,
        sortPriority: isValid ? 1 : 2 // 有效卡片优先级高
      };
    });
    
    // 排序
    const sortedCards = cardsWithStatus.sort((a, b) => {
      // 首先按优先级排序（有效卡片在前）
      if (a.sortPriority !== b.sortPriority) {
        return a.sortPriority - b.sortPriority;
      }
      
      if (a.isValid && b.isValid) {
        // 有效卡片按到期时间升序排序
        const timeA = new Date(a.validTo).getTime();
        const timeB = new Date(b.validTo).getTime();
        
        if (timeA !== timeB) {
          return timeA - timeB;
        }
        
        // 到期时间相同则按创建时间升序
        return new Date(a.createTime).getTime() - new Date(b.createTime).getTime();
      } else {
        // 无效卡片按创建时间降序排序（最新的在前）
        return new Date(b.createTime).getTime() - new Date(a.createTime).getTime();
      }
    });
    
    return sortedCards;
  } catch (error) {
    console.error('获取所有会员卡失败:', error);
    throw error;
  }
}

/**
 * 智能扣减会员卡次数
 * 
 * 扣减策略：
 * 1. 选择到期时间最近的有效卡片
 * 2. 使用数据库事务确保原子性
 * 3. 返回被扣减的卡片信息
 * 
 * @param {string} userId - 用户openid
 * @param {Object} db - 数据库实例
 * @returns {Promise<Object>} 扣减结果 {success: boolean, card?: Object, message?: string}
 */
export async function deductMembershipCard(userId, db) {
  try {
    // 获取有效的会员卡（已按优先级排序）
    const validCards = await getValidMembershipCards(userId, db);
    
    if (validCards.length === 0) {
      return {
        success: false,
        message: '没有有效的会员卡，无法扣减次数'
      };
    }
    
    // 选择优先级最高的卡片（数组第一个）
    const selectedCard = validCards[0];
    
    // 检查剩余次数
    if (selectedCard.remainingTimes <= 0) {
      return {
        success: false,
        message: '会员卡剩余次数不足'
      };
    }
    
    // 扣减次数（使用原子操作）
    await db.collection('membershipCard').doc(selectedCard._id).update({
      data: {
        remainingTimes: selectedCard.remainingTimes - 1,
        updateTime: new Date()
      }
    });
    
    return {
      success: true,
      card: {
        ...selectedCard,
        remainingTimes: selectedCard.remainingTimes - 1
      },
      message: `已扣减会员卡 ${selectedCard.cardNumber} 1次，剩余 ${selectedCard.remainingTimes - 1} 次`
    };
    
  } catch (error) {
    console.error('扣减会员卡次数失败:', error);
    return {
      success: false,
      message: '扣减失败: ' + error.message
    };
  }
}

/**
 * 恢复会员卡次数（取消预约时使用）
 * 
 * @param {string} userId - 用户openid
 * @param {string} cardNumber - 会员卡号
 * @param {Object} db - 数据库实例
 * @returns {Promise<Object>} 恢复结果 {success: boolean, card?: Object, message?: string}
 */
export async function restoreMembershipCard(userId, cardNumber, db) {
  try {
    // 查找对应的会员卡
    const cardResult = await db.collection('membershipCard')
      .where({
        userId: userId,
        cardNumber: cardNumber,
        status: CARD_STATUS.NORMAL
      })
      .get();
    
    if (cardResult.data.length === 0) {
      return {
        success: false,
        message: '未找到对应的会员卡'
      };
    }
    
    const card = cardResult.data[0];
    
    // 恢复次数（使用原子操作）
    await db.collection('membershipCard').doc(card._id).update({
      data: {
        remainingTimes: card.remainingTimes + 1,
        updateTime: new Date()
      }
    });
    
    return {
      success: true,
      card: {
        ...card,
        remainingTimes: card.remainingTimes + 1
      },
      message: `已恢复会员卡 ${cardNumber} 1次，当前剩余 ${card.remainingTimes + 1} 次`
    };
    
  } catch (error) {
    console.error('恢复会员卡次数失败:', error);
    return {
      success: false,
      message: '恢复失败: ' + error.message
    };
  }
}

/**
 * 计算会员卡的显示状态
 * 
 * @param {Object} card - 会员卡对象
 * @returns {Object} 状态信息 {status: string, isExpired: boolean, isExpiring: boolean, isValid: boolean}
 */
export function calculateCardStatus(card) {
  const now = new Date();
  const validTo = new Date(card.validTo);
  const validFrom = new Date(card.validFrom);
  
  let status = card.status;
  let isExpired = false;
  let isExpiring = false;
  let isValid = false;
  
  if (card.status === CARD_STATUS.NORMAL) {
    if (now > validTo) {
      status = CARD_STATUS.EXPIRED;
      isExpired = true;
    } else if (card.remainingTimes <= 0) {
      status = CARD_STATUS.EXHAUSTED;
    } else if (now >= validFrom && now <= validTo) {
      isValid = true;
      // 检查是否即将到期（7天内）
      isExpiring = (validTo - now) < 7 * 24 * 60 * 60 * 1000;
    }
  }
  
  return {
    status,
    isExpired,
    isExpiring,
    isValid
  };
}

/**
 * 格式化日期显示
 * 
 * @param {Date|string} date - 日期对象或字符串
 * @returns {string} 格式化后的日期字符串 YYYY-MM-DD
 */
export function formatDate(date) {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const day = d.getDate().toString().padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}

/**
 * 获取会员卡统计信息
 * 
 * @param {Array} cards - 会员卡列表
 * @returns {Object} 统计信息
 */
export function getCardStatistics(cards) {
  const stats = {
    total: cards.length,
    valid: 0,
    expired: 0,
    exhausted: 0,
    frozen: 0,
    totalRemainingTimes: 0,
    expiringSoon: 0 // 7天内到期的卡片数量
  };
  
  cards.forEach(card => {
    const cardStatus = calculateCardStatus(card);
    
    if (cardStatus.isValid) {
      stats.valid++;
      stats.totalRemainingTimes += card.remainingTimes;
      
      if (cardStatus.isExpiring) {
        stats.expiringSoon++;
      }
    } else if (cardStatus.status === CARD_STATUS.EXPIRED) {
      stats.expired++;
    } else if (cardStatus.status === CARD_STATUS.EXHAUSTED) {
      stats.exhausted++;
    } else if (cardStatus.status === CARD_STATUS.FROZEN) {
      stats.frozen++;
    }
  });
  
  return stats;
}
