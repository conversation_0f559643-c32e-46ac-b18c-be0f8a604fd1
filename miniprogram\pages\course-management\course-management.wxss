/**
 * course-management.wxss - 课程管理页面样式文件
 *
 * 样式设计理念：
 * 1. 现代化管理界面：采用卡片式设计，层次分明
 * 2. 响应式布局：适配不同屏幕尺寸的设备
 * 3. 交互反馈：丰富的动画效果和状态变化
 * 4. 一致性：与整个应用的设计语言保持统一
 *
 * 技术特点：
 * - 使用Flexbox布局实现复杂的页面结构
 * - CSS3动画提升用户体验
 * - 响应式设计适配移动端
 * - 组件化样式便于维护和复用
 *
 * 与您熟悉的技术对比：
 * - 布局方式类似于WPF的Grid和StackPanel
 * - 样式继承类似于CSS的级联特性
 * - 动画效果类似于WPF的Storyboard
 * - 响应式设计类似于Bootstrap的栅格系统
 */

/*
 * 页面根元素样式
 * 设置页面的基础高度，为固定布局提供基础
 * 在小程序中，page是页面的根元素
 */
page {
  height: 100%;
}

/*
 * 页面容器样式
 * 确保页面容器占满整个可用高度
 */
.page {
  height: 100%;
}

/**
 * .container: 页面根容器样式
 *
 * 作用：定义整个页面的基础布局和外观
 * 设计原则：为内容提供合适的间距和背景
 */
.container {
  /*
   * 固定布局设置
   * 禁止根容器滚动，让顶部区域固定，只有内容区域滚动
   * 使用小程序支持的单位和方式
   */
  height: 100%; /* 占满父容器高度 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 禁止根容器滚动 */

  /*
   * padding: 内边距设置
   * 16px: 为页面内容提供舒适的边距
   * 避免内容紧贴屏幕边缘，提升阅读体验
   */
  padding: 16px;

  /*
   * padding-bottom: 底部内边距优化
   *
   * 由于course-management页面不显示底部TabBar，
   * 可以让内容区域充分利用整个屏幕高度
   *
   * 组成部分：
   * - 16px: 基础底部间距，保持与顶部一致
   * - env(safe-area-inset-bottom): iPhone X等全面屏设备的底部安全区域
   *
   * 移除了120rpx的TabBar预留空间，让内容区域更大
   */
  padding-bottom: calc(16px + env(safe-area-inset-bottom));

  /*
   * background-color: 背景颜色
   * #f5f5f5: 浅灰色背景
   *
   * 设计考虑：
   * - 与白色卡片形成对比，突出内容层次
   * - 减少视觉疲劳，适合长时间使用
   * - 符合现代扁平化设计趋势
   */
  background-color: #f5f5f5;

  /*
   * box-sizing: 盒模型计算方式
   * border-box: 宽高包含padding和border
   *
   * 优势：
   * - 设置width: 100%时，元素实际占用就是100%
   * - 避免因padding导致的布局溢出问题
   * - 更符合直觉的尺寸计算方式
   */
  box-sizing: border-box;

  /*
   * width: 100% - 宽度占满父容器
   * 确保容器充分利用可用空间
   */
  width: 100%;

  /*
   * overflow-x: hidden - 隐藏水平溢出内容
   * 防止内容水平滚动，保持页面整洁
   * 特别适用于移动端，避免意外的横向滚动
   */
  overflow-x: hidden;
}

/**
 * 顶部区域样式设计 - TDesign风格重新设计
 *
 * 设计理念：紧凑、现代、一致性
 * 布局结构：垂直堆叠，减少水平空间占用
 */

/**
 * .top-section: 顶部区域容器 - 美化版
 *
 * 功能：包含主选项卡，移除操作按钮到搜索行
 * 布局：优雅的容器设计，与美化后的选项卡协调
 */
.top-section {
  /*
   * 固定顶部区域样式
   * 让顶部选项卡固定在顶部，不随内容滚动
   */
  flex-shrink: 0; /* 防止被压缩 */
  width: 100%;
  margin-bottom: 8px; /* 与下方内容的间距 */

  /*
   * 背景设置 - 移除背景，让选项卡容器的背景生效
   */
  background: transparent;

  /*
   * 移除边框 - 让选项卡容器处理边框
   */
  border: none;

  /*
   * 相对定位
   */
  position: relative;

  /*
   * 层级控制
   */
  z-index: 100;

  /* 字体设置：继承全局字体配置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * .top-tabs-section: 选项卡区域容器 - 美化版线条选项卡
 *
 * 功能：包含主选项卡组件
 * 布局：优雅的线条式设计，增强视觉层次感
 */
.top-tabs-section {
  /*
   * 宽度和布局
   */
  
  position: relative;

  /*
   * 背景设计 - 渐变背景增加层次感
   */
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);

  /*
   * 边框设计 - 精致的边框效果
   */
  border: 1px solid #f0f0f0;
  /*border-radius: 8px 8px 0 0;  上圆角，下直角 */
  border-radius: 8px;


  /*
   * 阴影效果 - 轻微的阴影增加浮起感
   */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.02),
    0 0 0 1px rgba(0, 0, 0, 0.01);

  /*
   * 内边距 - 适当的内边距
   * 增加4px底部内边距，为激活指示器预留空间
   */
  padding: 0 16px 4px;

  /*
   * 过渡动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 底部装饰线 - 与内容区域的分隔
   */
  border-bottom: 1px solid #e7e7e7;

  /*
   * 层级控制
   */
  z-index: 10;
}

/*
 * 悬停效果 - 增加交互反馈
 */
.top-tabs-section:hover {
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(0, 82, 217, 0.08);
}

/**
 * .custom-top-tabs: 自定义线条选项卡样式 - 美化版
 *
 * 功能：覆盖TDesign组件的默认样式
 * 目的：实现优雅的线条选项卡设计
 */
.custom-top-tabs {
  /*
   * 背景 - 完全透明，让容器背景显示
   */
  background-color: transparent;
  border: none;

  /*
   * 移除圆角 - 线条选项卡不需要圆角
   */
  border-radius: 0;

  /*
   * 溢出控制
   */
  overflow: visible;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 16px;

  /*
   * 布局控制
   */
  margin: 0;
  width: 100%;
  height: auto;

  /*
   * 最小高度设置为最小值以确保紧凑布局
   */
  min-height: 1px;
  }

  /**
 * TDesign线条选项卡导航区域样式 - 美化版
 */
.custom-top-tabs .t-tabs__nav {
  /*
   * 内边距 - 优化的内边距
   */
  padding: 0;
  height: auto;
  min-height: 1px;

  /*
   * 移除底部边框，使用父容器的边框
   */
  border-bottom: none;

  /*
   * 布局优化
   */
  display: flex;
  align-items: center;

  /*
   * 背景渐变效果
   */
  background: transparent;
}

/**
 * 线条选项卡项目样式 - 美化版
 */
.custom-top-tabs .t-tabs__item {
  /*
   * 字体设置 - 优化可读性
   */
  font-size: 16px !important;
  font-weight: 500;

  /*
   * 内边距 - 增加舒适的点击区域
   */
  padding: 14px 20px !important;

  /*
   * 高度控制
   */
  height: auto;
  line-height: 1.4;
  min-height: 44px;

  /*
   * 布局控制
   */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  /*
   * 移除圆角和背景
   */
  border-radius: 0;
  background: transparent !important;

  /*
   * 底部边框 - 激活状态指示器
   */
  border-bottom: 3px solid transparent; /* 增加指示器厚度 */

  /*
   * 过渡动画 - 更流畅的动画
   */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /*
   * 文字颜色
   */
  color: #666666 !important;

  /*
   * 相对定位用于伪元素
   */
  position: relative;
}

/*
 * 选项卡项目的装饰效果
 */
.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/**
 * 激活状态的线条选项卡 - 美化版
 */
.custom-top-tabs .t-tabs__item--active {
  /*
   * 文字颜色和字重
   */
  color: #0052d9 !important;
  font-weight: 600 !important;

  /*
   * 底部蓝色指示线 - 更粗更明显
   */
  border-bottom-color: #0052d9 !important;

  /*
   * 保持透明背景
   */
  background: transparent !important;

  /*
   * 文字阴影效果 - 增加层次感
   */
  text-shadow: 0 0 1px rgba(0, 82, 217, 0.1);
}

/*
 * 激活状态的顶部装饰线
 */
.custom-top-tabs .t-tabs__item--active::before {
  width: 60%; /* 激活时显示顶部装饰线 */
}

/**
 * 非激活状态的选项卡悬停效果 - 美化版
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover {
  /*
   * 悬停时的文字颜色
   */
  color: #333333 !important;

  /*
   * 保持透明背景
   */
  background: transparent !important;

  /*
   * 悬停时的底部边框效果
   */
  border-bottom-color: rgba(0, 82, 217, 0.3) !important;

  /*
   * 轻微的文字阴影
   */
  text-shadow: 0 0 1px rgba(51, 51, 51, 0.1);
}

/*
 * 悬停时的顶部装饰线
 */
.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover::before {
  width: 30%; /* 悬停时显示较短的顶部装饰线 */
}

/**
 * 底部指示线容器
 */
.custom-top-tabs .t-tabs__track {
  display: none; /* 隐藏默认的滑动指示器，使用border-bottom代替 */
}

/**
 * .top-actions: 操作按钮区域
 *
 * 功能：包含添加按钮等操作元素
 * 布局：固定宽度，不参与弹性伸缩
 */
.top-actions {
  /*
   * width: fit-content - 宽度适应内容
   *
   * fit-content：CSS的内容适应值
   * 效果：宽度刚好包含内部内容，不多不少
   * 适用于按钮容器等固定内容的场景
   */
  width: fit-content;

  /* display: flex - 弹性布局 */
  display: flex;

  /* align-items: center - 垂直居中 */
  align-items: center;

  /*
   * gap: 8px - 按钮间距
   * 当有多个按钮时，提供合适的间距
   */
  gap: 8px;

  /*
   * flex-shrink: 0 - 禁止收缩
   *
   * 作用：确保按钮区域不会因为空间不足而被压缩
   * 保证按钮始终保持可用的尺寸
   */
  flex-shrink: 0;
}

/**
 * 顶部操作按钮样式覆盖
 *
 * 目的：自定义TDesign按钮组件的外观
 * 使用!important：强制覆盖组件库的默认样式
 *
 * 设计理念：
 * 与页面整体设计保持一致，突出操作按钮的重要性
 */
.top-actions button.t-button {
  /*
   * background: #52c41a - 绿色背景
   *
   * 颜色选择：
   * #52c41a是Ant Design的success色彩
   * 表示积极、正面的操作（添加、创建）
   * 与页面的蓝色主题形成良好的对比
   */
  background: #52c41a !important;

  /*
   * border: none - 移除边框
   * 现代扁平化设计趋势，简洁清爽
   */
  border: none !important;

  /*
   * color: #fff - 白色文字
   * 与绿色背景形成高对比度，确保可读性
   */
  color: #fff !important;

  /*
   * box-shadow: none - 移除阴影
   * 与页面的扁平化设计风格保持一致
   */
  box-shadow: none !important;

  /*
   * border-radius: 8px - 圆角设计
   * 与页面其他元素的圆角保持一致
   * 8px是页面的标准圆角尺寸
   */
  border-radius: 8px !important;

  /*
   * font-weight: 500 - 中等字重
   * 比normal(400)稍重，比bold(700)稍轻
   * 突出按钮文字但不过于厚重
   */
  font-weight: 500 !important;

  /*
   * margin-left: 12px - 左边距
   * 与其他元素保持适当间距
   * 12px是页面的标准间距单位
   */
  margin-left: 12px !important;

  /*
   * transition: background 0.2s - 背景色过渡动画
   * 0.2s的过渡时间提供平滑的交互反馈
   * 只对background属性应用过渡，避免影响其他属性
   */
  transition: background 0.2s !important;

  /*
   * padding: 0 28px - 内边距设置
   * 水平内边距28px提供足够的点击区域
   * 垂直内边距为0，由组件默认高度控制
   */
  padding: 0 28px !important;

  /*
   * 宽度控制：重置组件默认的宽度限制
   *
   * min-width: unset - 移除最小宽度限制
   * max-width: unset - 移除最大宽度限制
   * width: auto - 宽度自适应内容
   *
   * 目的：让按钮宽度完全由内容和padding决定
   */
  min-width: unset !important;
  max-width: unset !important;
  width: auto !important;
}

/**
 * 按钮交互状态样式
 *
 * :active - 按下状态
 * :hover - 悬停状态（主要用于PC端）
 *
 * 移动端主要使用:active状态
 * PC端两种状态都会生效
 */
.top-actions button.t-button:active,
.top-actions button.t-button:hover {
  /*
   * background: #389e0d - 深绿色背景
   * 比默认绿色更深，提供明显的状态反馈
   * 符合用户对按钮交互的心理预期
   */
  background: #389e0d !important;

  /*
   * color: #fff - 保持白色文字
   * 确保在深色背景下的文字可读性
   */
  color: #fff !important;
}

/**
 * 筛选区域样式 - 固定在顶部，不参与滚动
 *
 * 功能：包含子选项卡和搜索功能的区域
 * 布局：固定在顶部，与top-section同级，不随内容滚动
 */
.filter-section {
  /*   * 固定布局 - 与top-section保持一致   */
  /*flex-shrink: 0;  防止被压缩 */
  width: 100%;
   /*margin: 0 auto 16px auto; 恢复margin，与其他固定区域保持间距 */

  /*   * 背景和视觉设计 - 卡片式容器   */
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e7e7e7;

  /*  内边距 - 紧凑设计*/
  padding:12px;

  /*如果没有下面这个，你加padding就会像外扩！*/
  box-sizing: border-box;

  /*   * 阴影效果   */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);

  /*
   * 内部布局 - 垂直堆叠
   */
  display: flex;
  flex-direction: column;
  gap: 8px; /* 子元素间距 */

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * 过渡动画
   */
  transition: all 0.2s ease;

  /*
   * 禁止滚动 - 防止用户在筛选区域滑动时触发页面滚动
   */
  overflow: hidden; /* 隐藏溢出内容 */
  touch-action: none; /* 禁用触摸手势，包括滚动、缩放等 */
  -webkit-overflow-scrolling: auto; /* 禁用iOS的弹性滚动 */
  overscroll-behavior: contain; /* 防止滚动传播到父元素 */

  /*
   * 层级控制 - 确保在内容之上
   */
  z-index: 90; /* 比top-section稍低，但高于内容区域 */

  /*
   * 相对定位
   */
   position: relative;
}

/*
 * 筛选区域内的主要子元素禁止滚动
 * 避免使用通配符选择器，改为具体的元素选择器
 */
.filter-section view,
.filter-section text,
.filter-section .booking-tabs,
.filter-section .search-actions-section,
.filter-section .collapsed-layout,
.filter-section .expanded-layout,
.filter-section .actions-container {
  touch-action: none; /* 禁用触摸手势 */
  -webkit-overflow-scrolling: auto; /* 禁用iOS弹性滚动 */
}

/*
 * 允许按钮和输入框的正常交互
 */
.filter-section .t-button,
.filter-section input,
.filter-section .search-input,
.filter-section .booking-tab,
.filter-section .search-icon-only,
.filter-section .clear-icon,
.filter-section .collapse-icon {
  touch-action: manipulation; /* 允许点击和双击，但禁止滚动和缩放 */
}

/**
 * 自定义子选项卡样式
 *
 * 功能：覆盖TDesign选项卡组件的默认样式
 * 目的：与页面整体设计保持一致
 */
.custom-sub-tabs {
  /*
   * background-color: #ffffff - 白色背景
   * 与页面的灰色背景形成对比
   * 突出选项卡区域
   */
  background-color: #ffffff;

  /*
   * border-radius: 8px - 圆角设计
   * 与页面其他卡片元素保持一致
   */
  border-radius: 8px;

  /*
   * overflow: hidden - 隐藏溢出内容
   * 确保子元素不会超出圆角边界
   * 保持整洁的视觉效果
   */
  overflow: hidden;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * margin: 0 - 清除默认外边距
   * 避免组件默认样式影响布局
   */
  margin: 0;

  /*
   * max-width: 100% - 最大宽度限制
   * 防止选项卡超出容器宽度
   */
  max-width: 100%;
}

/**
 * TDesign选项卡导航区域样式覆盖
 *
 * .t-tabs__nav：TDesign组件的内部类名
 * 使用后代选择器精确控制组件内部样式
 */
.custom-sub-tabs .t-tabs__nav {
  /*
   * padding: 0 8px - 水平内边距
   * 为选项卡内容提供适当的左右间距
   * 避免内容紧贴边缘
   */
  padding: 0 8px;
}

/**
 * 课程列表容器样式
 *
 * 功能：包含所有课程卡片的容器
 * 布局：垂直排列的课程卡片列表
 */
.course-list {
  /*
   * margin-bottom: 16px - 底部间距
   * 与页面底部或其他元素保持距离
   */
  margin-bottom: 0px;

  /*
   * width: 100% - 占满父容器宽度
   * 确保列表在不同屏幕尺寸下的适配
   */
  width: 100%;

  /* 字体设置：继承全局字体配置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * box-sizing: border-box - 盒模型设置
   * 宽高包含padding和border
   * 避免因内边距导致的布局问题
   */
  box-sizing: border-box;
}

/**
 * 时间轴日期分隔样式
 *
 * 设计参考：my-bookings页面的时间轴设计
 * 功能：为相同日期的课程提供视觉分组
 *
 * 设计理念：
 * 类似于微信聊天记录的日期分隔线
 * 提供清晰的时间导航和内容分组
 */
.timeline-date {
  /*
   * margin: 16px 0 8px 0 - 外边距设置
   *
   * 16px top: 与上方内容保持较大距离，突出分组效果
   * 0 left/right: 不需要水平外边距
   * 8px bottom: 与下方课程卡片保持适中距离
   */
  /* margin: 16px 0 8px 0; */
       margin:8px;

  /*
   * font-size: 14px - 统一正文字体大小
   * 使用统一的14px正文字体，保持页面一致性
   */
  font-size: 14px;

  /*
   * color: #0052d9 - 蓝色文字
   *
   * #0052d9：TDesign的主色调
   * 与页面整体色彩保持一致
   * 蓝色传达信息性、导航性的含义
   */
  color: #0052d9;

  /*
   * font-weight: bold - 粗体字重
   * 增强日期信息的视觉权重
   * 便于用户快速扫描和定位
   */
  font-weight: bold;

  /*
   * text-align: left - 左对齐
   * 与页面内容的对齐方式保持一致
   * 符合从左到右的阅读习惯
   */
  text-align: left;

  /*
   * position: relative - 相对定位
   * 为伪元素的绝对定位提供参考点
   * 不影响正常的文档流
   */
  position: relative;

  /*
   * padding-left: 16px - 左内边距
   * 为左侧的圆点图标预留空间
   * 确保文字不会与图标重叠
   */
  padding-left: 16px;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/**
 * 时间轴圆点装饰
 *
 * 使用CSS伪元素创建装饰性圆点
 * 模拟真实时间轴的视觉效果
 */
.timeline-date::before {
  /*
   * content: '' - 空内容
   * 伪元素必须设置content属性才能显示
   * 空字符串表示不显示文字内容
   */
  content: '';

  /*
   * position: absolute - 绝对定位
   * 相对于父元素（.timeline-date）进行定位
   * 不占用正常的文档流空间
   */
  position: absolute;

  /*
   * left: 0 - 左边距为0
   * 紧贴父元素的左边缘
   */
  left: 0;

  /*
   * top: 50% - 垂直居中定位
   * 相对于父元素高度的50%位置
   */
  top: 50%;

  /*
   * 圆点尺寸设置
   *
   * width: 8px, height: 8px
   * 创建8x8像素的正方形
   * 配合border-radius: 50%形成完美圆形
   */
  width: 8px;
  height: 8px;

  /*
   * background: #0052d9 - 圆点颜色
   * 与文字颜色保持一致
   * 形成统一的视觉元素
   */
  background: #0052d9;

  /*
   * border-radius: 50% - 圆形边框
   * 将正方形转换为圆形
   * 50%表示圆角半径为宽高的一半
   */
  border-radius: 50%;

  /*
   * transform: translateY(-50%) - 垂直居中调整
   *
   * 配合top: 50%实现真正的垂直居中
   * translateY(-50%)将元素向上移动自身高度的50%
   * 这是CSS垂直居中的经典技巧
   */
  transform: translateY(-50%);
}

/**
 * 课程卡片基础样式
 *
 * 功能：单个课程信息的容器
 * 设计：现代卡片式设计，支持交互反馈
 */

.course-card {
  /*
   * background-color: #ffffff - 白色背景
   * 与页面灰色背景形成对比
   * 突出卡片内容区域
   */
  background-color: #ffffff;

  /*
   * border-radius: 12px - 圆角设计
   *
   * 12px：比标准8px稍大的圆角
   * 为卡片提供更柔和、现代的外观
   * 符合当前移动应用的设计趋势
   */
  border-radius: 12px;

  /*
   * padding: 12px - 内边距
   * 为卡片内容提供舒适的内部空间
   * 12px是页面的标准间距单位
   */
  padding: 12px;

  /*
   * margin-bottom: 16px - 底部外边距
   * 卡片之间的垂直间距
   * 保持列表的视觉节奏
   */
  margin-bottom: 8px;

  /*
   * box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) - 阴影效果
   *
   * 参数解析：
   * - 0: 水平偏移（无偏移）
   * - 2px: 垂直偏移（向下2px）
   * - 8px: 模糊半径（8px的模糊效果）
   * - rgba(0, 0, 0, 0.08): 半透明黑色（8%透明度）
   *
   * 效果：轻微的向下阴影，营造卡片浮起的视觉效果
   */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /* 盒模型设置 */
  box-sizing: border-box;

  /*
   * width: 100% - 占满父容器宽度
   * 确保卡片充分利用可用空间
   */
  width: 100%;

  /*
   * overflow: hidden - 隐藏溢出内容
   * 确保内容不会超出卡片边界
   * 保持整洁的视觉效果
   */
  overflow: hidden;

  /*
   * cursor: pointer - 鼠标指针样式
   * 表明卡片可以点击
   * 主要用于PC端的用户体验
   */
  cursor: pointer;

  /*
   * transition: all 0.2s ease - 过渡动画
   *
   * all: 对所有可动画属性应用过渡
   * 0.2s: 动画持续时间
   * ease: 缓动函数（先快后慢）
   *
   * 用途：为交互状态变化提供平滑过渡
   */
  transition: all 0.2s ease;
}

/**
 * 课程卡片激活状态样式
 *
 * :active伪类：用户按下卡片时的状态
 * 提供即时的触觉反馈
 */
.course-card:active {
  /*
   * transform: scale(0.98) - 缩放变换
   *
   * scale(0.98): 缩放到原尺寸的98%
   * 模拟按下的物理反馈
   * 让用户感受到"按下"的效果
   */
  transform: scale(0.98);

  /*
   * box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12) - 调整阴影
   *
   * 相比默认阴影：
   * - 垂直偏移减少：2px → 1px
   * - 模糊半径减少：8px → 4px
   * - 透明度增加：0.08 → 0.12
   *
   * 效果：模拟卡片被按下，阴影变小变深
   */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12);
}

/**
 * 新加载卡片的滑入动画
 *
 * 设计一致性：与schedule页面保持一致
 * 用户体验：为新内容提供优雅的入场效果
 */
.course-card.slide-in {
  /*
   * animation: slide-in-up 0.6s ease-out
   *
   * slide-in-up: 动画名称（在下方定义）
   * 0.6s: 动画持续时间
   * ease-out: 缓动函数（先快后慢）
   *
   * 效果：卡片从下方滑入到正确位置
   */
  animation: slide-in-up 0.6s ease-out;
}

/**
 * 滑入动画关键帧定义
 *
 * 动画效果：从下方透明滑入到正常位置
 * 提供流畅的视觉过渡体验
 */
@keyframes slide-in-up {
  /**
   * 动画关键帧：0%状态（动画开始）
   *
   * opacity: 0 - 完全透明
   * transform: translateY(20px) - 向下偏移20像素
   *
   * 效果：元素从下方20像素处开始，完全透明
   */
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  /**
   * 动画关键帧：100%状态（动画结束）
   *
   * opacity: 1 - 完全不透明
   * transform: translateY(0) - 回到原始位置
   *
   * 效果：元素移动到正确位置，完全可见
   *
   * 整体动画效果：
   * 元素从下方透明滑入到正确位置并显示
   * 提供优雅的入场效果，增强用户体验
   */
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/**
 * 课程卡片头部样式
 *
 * 功能：包含课程标题和状态标签的区域
 * 布局：左右分布，标题占主要空间，状态标签固定宽度
 */
.course-header {
  /*
   * display: flex - 弹性布局
   * 实现左右分布的布局效果
   */
  display: flex;

  /*
   * justify-content: space-between - 主轴对齐方式
   * 左右两端对齐，中间自动分配空间
   * 确保标题和状态标签分别位于两端
   */
  justify-content: space-between;

  /*
   * align-items: center - 交叉轴对齐方式
   * 垂直居中对齐，确保标题和状态标签在同一水平线上
   */
  align-items: center;

  /*
   * margin-bottom: 8px - 压缩底部外边距
   * 减少间距，让布局更紧凑
   */
  margin-bottom: 8px;

  /*
   * gap: 8px - 子元素间距
   * 标题和状态标签之间的最小间距
   * 防止内容过长时两者重叠
   */
  gap: 8px;
}

/**
 * 课程标题样式
 *
 * 功能：显示课程名称，是卡片最重要的信息
 * 设计：突出显示，支持长文本的优雅处理
 */
.course-title {
  /*
   * font-size: 16px - 统一主标题字体大小
   * 统一使用16px作为主标题字体，保持页面一致性
   */
  font-size: 16px;

  /*
   * font-weight: 600 - 字体粗细
   * 半粗体，介于normal(400)和bold(700)之间
   * 提供适度的视觉重量，不会过于厚重
   */
  font-weight: 600;

  /*
   * color: #333 - 文字颜色
   * 深灰色，提供良好的对比度和可读性
   * 比纯黑色(#000)更柔和，减少视觉疲劳
   */
  color: #333;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * flex: 1 - 弹性增长
   * 占据除状态标签外的所有剩余空间
   * 确保标题有足够的显示区域
   */
  flex: 1;

  /*
   * 文本溢出处理
   *
   * overflow: hidden - 隐藏溢出内容
   * text-overflow: ellipsis - 用省略号表示被截断的文本
   * white-space: nowrap - 禁止文本换行
   *
   * 组合效果：
   * 当标题过长时，显示为"很长的课程标题..."
   * 保持布局整洁，避免文本换行破坏设计
   */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/**
 * 课程状态标签基础样式
 *
 * 功能：显示课程的当前状态（已上线、未上线、已结束等）
 * 设计：小巧的标签样式，不同状态有不同的颜色
 */
.course-status {
  /*
   * padding: 4px 8px - 内边距
   * 垂直4px，水平8px
   * 为文字提供适当的内部空间，形成标签效果
   */
  padding: 4px 8px;

  /*
   * border-radius: 4px - 圆角
   * 小圆角设计，现代化的标签外观
   * 与卡片的圆角风格保持协调
   */
  border-radius: 4px;

  /*
   * font-size: 12px - 字体大小
   * 比标题小，表明这是辅助信息
   * 在小标签中仍保持可读性
   */
  font-size: 12px;

  /*
   * font-weight: 500 - 字体粗细
   * 中等粗细，确保在小尺寸下的清晰度
   */
  font-weight: 500;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * flex-shrink: 0 - 禁止收缩
   * 确保状态标签不会因为标题过长而被压缩
   * 保持标签的最小可读尺寸
   */
  flex-shrink: 0;

  /*
   * white-space: nowrap - 禁止换行
   * 确保状态文字在一行内显示
   * 保持标签的紧凑外观
   */
  white-space: nowrap;
}

/**
 * 课程状态标签 - 可预约状态
 *
 * 使用场景：课程有剩余名额，用户可以预约
 * 颜色语义：绿色系，表示积极、可用的状态
 */
.course-status.available {
  /*
   * background-color: #e8f5e8 - 浅绿色背景
   * 柔和的绿色，不会过于刺眼
   * 传达"可用"、"正常"的积极信息
   */
  background-color: #e8f5e8;

  /*
   * color: #52c41a - 深绿色文字
   * 与背景形成适当对比，确保可读性
   * 绿色系统一，强化"可用"的语义
   */
  color: #52c41a;
}

/**
 * 课程状态标签 - 已预约状态
 *
 * 使用场景：用户已经预约了该课程
 * 颜色语义：蓝色系，表示信息性、已确认的状态
 */
.course-status.booked {
  /*
   * background-color: #e6f3ff - 浅蓝色背景
   * 清爽的蓝色，传达"已确认"、"信息性"的含义
   */
  background-color: #e6f3ff;

  /*
   * color: #0052d9 - 深蓝色文字
   * TDesign的主色调，与整体设计保持一致
   * 蓝色传达稳定、可信的感觉
   */
  color: #0052d9;
}

/**
 * 课程状态标签 - 已满员状态
 *
 * 使用场景：课程名额已满，无法继续预约
 * 颜色语义：橙色系，表示警告、注意的状态
 */
.course-status.full {
  /*
   * background-color: #fff2e8 - 浅橙色背景
   * 温和的橙色，表示需要注意但不是严重问题
   */
  background-color: #fff2e8;

  /*
   * color: #fa8c16 - 深橙色文字
   * 橙色传达"警告"、"限制"的含义
   * 提醒用户该课程不可预约
   */
  color: #fa8c16;
}

/**
 * 课程状态标签 - 已结束状态
 *
 * 使用场景：课程已经结束，无法进行任何操作
 * 颜色语义：灰色系，表示非活跃、历史状态
 */
.course-status.ended {
  /*
   * background-color: #f0f0f0 - 浅灰色背景
   * 中性的灰色，表示非活跃状态
   */
  background-color: #f0f0f0;

  /*
   * color: #888 - 深灰色文字
   * 降低视觉权重，表明这是历史信息
   * 不会干扰用户对活跃课程的关注
   */
  color: #888;
}

/**
 * 课程状态标签 - 已上线状态
 *
 * 使用场景：管理员视角，课程已发布可供用户预约
 * 颜色语义：绿色系，表示正常运营状态
 */
.course-status.online {
  /*
   * 与available状态使用相同的颜色
   * 在管理端，"已上线"等同于"可用"状态
   */
  background-color: #e8f5e8;
  color: #52c41a;
}

/**
 * 课程状态标签 - 未上线状态
 *
 * 使用场景：管理员视角，课程尚未发布，用户不可见
 * 颜色语义：橙色系，表示待处理、草稿状态
 */
.course-status.offline {
  /*
   * 与full状态使用相同的颜色
   * 橙色表示需要管理员注意和处理
   */
  background-color: #fff2e8;
  color: #fa8c16;
}

/**
 * 课程状态标签 - 无状态
 *
 * 使用场景：数据异常或状态未定义的兜底显示
 * 颜色语义：灰色系，表示未知或异常状态
 */
.course-status.no-status {
  /*
   * background-color: #f0f0f0 - 浅灰色背景
   * 中性色，不传达特定含义
   */
  background-color: #f0f0f0;

  /*
   * color: #999 - 中灰色文字
   * 比ended状态稍浅，表示这是异常情况
   * 提醒开发者或管理员检查数据
   */
  color: #999;
}

/**
 * 模板状态标签
 *
 * 使用场景：模板卡片中显示"模板"标识
 * 颜色语义：蓝色系，表示模板类型，区分于活动状态
 * 设计：照搬活动状态标签的样式，但使用不同的颜色
 */
.course-status.template-status {
  /*
   * background-color: #e6f3ff - 浅蓝色背景
   * 与活动的"已预约"状态使用相同颜色
   * 蓝色传达"信息性"、"模板"的含义
   */
  background-color: #e6f3ff;
  /*
   * color: #1890ff - 蓝色文字
   * 与背景形成良好对比，保证可读性
   * 与TDesign的primary主题色保持一致
   */
  color: #1890ff;
}

/**
 * 课程信息列表容器样式
 *
 * 功能：包含课程详细信息的区域
 * 布局：垂直排列的信息项目列表
 */
.course-info-list {
  /*
   * margin-bottom: 10px - 压缩底部外边距
   * 减少间距，让布局更紧凑
   */
  margin-bottom: 10px;
}

/**
 * 信息项目样式
 *
 * 功能：单个信息项的容器（时间、教练、场地等）
 * 布局：水平排列，图标+文字的组合
 */
.info-item {
  /*
   * display: flex - 弹性布局
   * 实现图标和文字的水平排列
   */
  display: flex;

  /*
   * align-items: center - 垂直居中对齐
   * 确保图标和文字在同一水平线上
   * 提供整齐的视觉效果
   */
  align-items: center;

  /*
   * margin-bottom: 6px - 底部外边距
   * 信息项之间的垂直间距
   * 保持适当的视觉分离
   */
  margin-bottom: 4px;

  /*
   * font-size: 14px - 字体大小
   * 标准的正文字体大小
   * 在移动端提供良好的可读性
   */
  font-size: 14px;

  /*
   * color: #666 - 文字颜色
   * 中等灰色，表示这是辅助信息
   * 比标题颜色浅，形成信息层次
   */
  color: #666;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * gap: 8px - 子元素间距
   * 图标和文字之间的固定间距
   * 确保布局的一致性
   */
  gap: 8px;
}

/**
 * 最后一个信息项的特殊处理
 *
 * :last-child伪类：选择最后一个子元素
 * 移除最后一项的底部边距，避免多余的空白
 */
.info-item:last-child {
  margin-bottom: 0;
}

/**
 * 信息项图标样式
 *
 * 选择器：.info-item t-icon
 * 后代选择器，选择信息项内的TDesign图标组件
 */
.info-item t-icon {
  /*
   * color: #0052d9 - 图标颜色
   * 使用主题蓝色，与页面整体色彩保持一致
   * 图标颜色比文字更突出，起到视觉引导作用
   */
  color: #0052d9;

  /*
   * flex-shrink: 0 - 禁止收缩
   * 确保图标不会因为文字过长而被压缩
   * 保持图标的标准尺寸和清晰度
   */
  flex-shrink: 0;
}

/**
 * 信息项文字样式
 *
 * 选择器：.info-item text
 * 选择信息项内的text元素
 */
.info-item text {
  /*
   * flex: 1 - 弹性增长
   * 占据除图标外的所有剩余空间
   * 确保文字有足够的显示区域
   */
  flex: 1;

  /*
   * 文本溢出处理
   *
   * overflow: hidden - 隐藏溢出内容
   * text-overflow: ellipsis - 用省略号表示被截断的文本
   * white-space: nowrap - 禁止文本换行
   *
   * 应用场景：
   * 当课程描述或场地名称过长时，优雅地截断显示
   * 保持卡片布局的整洁性
   */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/**
 * 课程卡片底部区域样式
 *
 * 功能：包含操作按钮的区域
 * 设计：与上方内容分离，突出操作区域
 */
.course-footer {
  /*
   * display: flex - 弹性布局
   * 为操作按钮提供灵活的排列方式
   */
  display: flex;

  /*
   * justify-content: space-between - 主轴对齐方式
   * 如果有多个元素，会分散对齐
   * 当前主要用于按钮组的布局
   */
  justify-content: space-between;

  /*
   * align-items: center - 交叉轴对齐方式
   * 垂直居中对齐，确保所有元素在同一水平线
   */
  align-items: center;

  /*
   * padding-top: 12px - 顶部内边距
   * 与上方信息列表保持距离
   * 配合border-top形成视觉分隔
   */
  padding-top: 12px;

  /*
   * border-top: 1px solid #f0f0f0 - 顶部边框
   *
   * 1px: 细线边框，不会过于突出
   * solid: 实线样式
   * #f0f0f0: 浅灰色，提供微妙的视觉分隔
   *
   * 作用：将操作区域与信息区域分开
   * 提供清晰的功能分组
   */
  border-top: 1px solid #f0f0f0;

  /* 字体设置 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  /*
   * gap: 8px - 子元素间距
   * 为可能的多个子元素提供间距
   */
  gap: 8px;
}

/**
 * 操作按钮容器样式
 *
 * 功能：包含所有操作按钮的容器
 * 布局：水平排列，支持滚动
 */
.action-buttons {
  /*
   * display: flex - 弹性布局
   * 水平排列操作按钮
   */
  display: flex;

  /*
   * align-items: center - 垂直居中对齐
   * 确保所有按钮在同一水平线上
   */
  align-items: center;

  /*
   * gap: 6px - 按钮间距
   * 比其他间距稍小，因为按钮本身有内边距
   * 保持紧凑但不拥挤的布局
   */
  gap: 6px;

  /*
   * flex-wrap: nowrap - 禁止换行
   * 强制所有按钮在一行内显示
   * 配合overflow-x实现水平滚动
   */
  flex-wrap: nowrap;

  /*
   * overflow-x: auto - 水平滚动
   * 当按钮过多时，允许水平滚动查看
   * 在小屏幕设备上特别有用
   *
   * 用户体验：
   * 保持按钮的完整显示，避免被截断
   * 用户可以滑动查看所有操作选项
   */
  overflow-x: auto;
  padding-bottom: 2px;
  min-width: 0;
}

.action-buttons .t-button {
  flex-shrink: 0;
  min-width: 60px;
  max-width: 80px;
}



.edit-disabled-btn {
  background-color: #f0f0f0 !important;
  border-color: #e0e0e0 !important;
  color: #bbb !important;
  cursor: not-allowed;
}

/* 空状态 */


/* 模板列表样式 */
.template-list {
  margin-bottom: 16px;
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
}

.template-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px; /* 压缩间距 */
  gap: 6px; /* 压缩间距 */
}

.template-title {
  font-size: 16px; /* 统一主标题字体大小 */
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-info-list {
  margin-bottom: 16px;
}

.template-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  gap: 8px;
}

/* 底部TabBar样式 */
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #e7e7e7;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.custom-tab-bar .t-tab-bar {
  background-color: #ffffff;
}

.custom-tab-bar .t-tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.custom-tab-bar .t-tab-bar-item__text {
  font-size: 20rpx;
  margin-top: 4rpx;
  color: #666666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.custom-tab-bar .t-tab-bar-item--active .t-tab-bar-item__text {
  color: #0052d9;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 已预约学员区域样式 */
.booked-students-section {
  margin: 12px 0;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.collapse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f8f8;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.collapse-content {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 0 16px; /* 与collapse-header保持一致的左右缩进 */
}

.student-list {
  padding: 8px 0;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  font-size: 14px;
  color: #666;
  justify-content: space-between; /* 确保移除按钮靠右对齐 */
}

.student-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

.student-name {
  flex: 1;
  margin-right: 8px; /* 为移除图标留出空间 */
}

/* 移除学员图标样式 */
.remove-student-icon {
  flex-shrink: 0; /* 防止图标被压缩 */
  margin-left: auto; /* 确保图标靠右对齐 */
  padding: 4px; /* 增加点击区域 */
  cursor: pointer; /* 鼠标悬停时显示手型 */
  border-radius: 4px; /* 圆角效果 */
  transition: background-color 0.2s; /* 平滑的背景色过渡 */
}

/* 移除图标悬停效果 */
.remove-student-icon:hover {
  background-color: rgba(227, 77, 89, 0.1); /* 浅红色背景 */
}

.no-students {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 20px 0;
}

/* 固定卡片宽度，防止内容拉伸 */
.course-card,
.template-card {
  width: 90vw;
  max-width: 700rpx;
  min-width: 320rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

.course-content,
.template-content {
  /*
   * 可滚动内容区域样式
   * 占用剩余空间，允许内容滚动
   */
  flex: 1; /* 占用剩余空间 */
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 禁止水平滚动 */

  width: 100%;
  max-width: 700rpx;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;

  /*
   * 滚动条样式优化（WebKit内核）
   * 在支持的浏览器中提供更美观的滚动条
   */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 课程状态选项卡样式 - TDesign风格优化 */
.booking-tabs {
 
  display: flex;
  background-color: #f8f9fa; /* 更柔和的背景色 */
  border-radius: 6px; /* 圆角 */
  padding: 8px; /* 内边距 */
  
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  border: 1px solid #e9ecef; /* 添加边框 */
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.02); /* 内阴影效果 */
}

.booking-tab {
  flex: 1;
  text-align: center;
   /* padding: 6px 8px;调整内边距，更紧凑 */
   padding: 6px;
  font-size: 14px; /* 确保字体不小于14px */
  border-radius: 4px; /* 圆角 */
  color: #666666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-weight: 500;
  transition: all 0.2s ease; /* 添加过渡动画 */
  cursor: pointer;
  
  display: flex;
  align-items: center;
  justify-content: center;
}

.booking-tab.active {
  background-color: #0052d9;
  color: #ffffff;
  font-weight: 600;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-shadow: 0 1px 3px rgba(0, 82, 217, 0.3); /* 激活状态阴影 */
}

/* 非激活状态的悬停效果 */
.booking-tab:not(.active):hover {
  background-color: #ffffff;
  color: #333333;
}

/* 骨架屏样式 */
.skeleton-card {
  background-color: #f3f3f3;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  width: 100%;
  min-height: 120px;
  animation: skeleton-fade 1.2s infinite linear;
}
.skeleton {
  background: linear-gradient(90deg, #f3f3f3 25%, #ececec 37%, #f3f3f3 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.2s infinite linear;
  border-radius: 6px;
}
.skeleton-text {
  height: 18px;
  width: 80%;
  margin-bottom: 8px; /* 压缩间距 */
}
.skeleton-block {
  height: 20px;
  width: 60px;
  margin-left: 8px;
}
.skeleton-btn {
  height: 28px;
  width: 60px;
  margin-right: 12px;
  display: inline-block;
}
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
@keyframes skeleton-fade {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 搜索和操作区域样式 - TDesign风格优化 */
.search-actions-section {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  justify-content: flex-start;
  box-sizing: border-box;
  overflow: visible;
  min-height: 36px; /* 减小高度，更紧凑 */

  /* 背景和边框 - 与筛选区域内部元素保持一致 */
  background: #f8f9fa;
  border-radius: 6px;
  padding: 6px; /* 减小内边距 */
  border: 1px solid #e9ecef;
}

/* 收起状态布局 */
.search-actions-section.collapsed {
  justify-content: flex-start;
}

.collapsed-layout {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;

  /*
   * 确保collapsed-layout容器能够正确处理子元素溢出
   * min-width: 0 - 允许flex子元素缩小到内容宽度以下
   * overflow: hidden - 防止子元素溢出到容器外
   */
  min-width: 0;
  overflow: hidden;
}

/* 展开状态布局 */
.search-actions-section.expanded {
  justify-content: flex-start;
}

.expanded-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex: 1; /* 占据search-actions-section的全部空间 */
}

/* 搜索图标状态 - TDesign风格优化 */
.search-icon-only {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px; /* 减小尺寸 */
  height: 32px; /* 减小尺寸 */
  background: #ffffff;
  border-radius: 4px; /* 减小圆角 */
  border: 1px solid #d9d9d9;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.search-icon-only:active {
  background: #f0f8ff;
  border-color: #0052d9;
  transform: scale(0.95); /* 添加按压效果 */
}

.search-toggle-icon {
  color: #666666;
  font-size: 14px; /* 统一图标大小 */
}

/* 展开的搜索输入框 - TDesign风格优化 */
.search-input-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 4px; /* 减小圆角 */
  padding: 6px 10px; /* 减小内边距 */
  border: 1px solid #d9d9d9;
  transition: all 0.2s ease;
  animation: searchExpand 0.3s ease-out;
  width: 100%;
  flex: 1;
  box-sizing: border-box;
  height: 32px; /* 固定高度，与搜索图标一致 */
}

.search-input-container:focus-within {
  border-color: #0052d9;
  box-shadow: 0 0 0 2px rgba(0, 82, 217, 0.1); /* 添加聚焦阴影 */
}

.search-icon {
  color: #999999;
  margin-right: 6px; /* 减小间距 */
  flex-shrink: 0;

  font-size: 14px; /* 统一图标大小 */
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px; /* 统一字体大小 */
  color: #333333;
  background: transparent;
  min-width: 0;
  height: 20px; /* 固定输入框高度 */
  line-height: 20px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.search-input::placeholder {
  color: #999999;
  font-size: 14px;
}

.clear-icon,
.collapse-icon {
  color: #999999;
  margin-left: 6px; /* 减小间距 */
  flex-shrink: 0;
  cursor: pointer;
  font-size: 14px; /* 统一图标大小 */
  padding: 4px; /* 增加点击区域 */
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-icon:active,
.collapse-icon:active {
  color: #0052d9;
  background: rgba(0, 82, 217, 0.1);
  transform: scale(0.95); /* 添加按压效果 */
}

/* 搜索展开动画 */
@keyframes searchExpand {
  from {
    opacity: 0;
    transform: scaleX(0.8);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

/* 操作按钮容器 - TDesign风格优化 */
.actions-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  margin-left: auto; /* 靠右对齐 */

  /*
   * 防止按钮超出屏幕边界的关键设置
   * max-width: 限制容器最大宽度，避免按钮超出屏幕
   * overflow-x: auto - 当按钮过多时允许水平滚动
   * 计算方式：100vw - 搜索图标宽度(32px) - 间距(16px) = 约70%
   */
  max-width: 70vw;
  overflow-x: auto;

  /*
   * 滚动条样式优化
   * 在小程序中，滚动条通常是系统默认样式
   * 这里设置一些基础的滚动行为
   */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
  scrollbar-width: none; /* Firefox隐藏滚动条 */
  -ms-overflow-style: none; /* IE隐藏滚动条 */
}

/* 隐藏webkit浏览器的滚动条 */
.actions-container::-webkit-scrollbar {
  display: none;
}

.actions-container .t-button {
  height: 32px; /* 固定高度 */
  padding: 0 12px; /* 减小内边距 */
  font-size: 14px; /* 统一字体大小 */

  /*
   * 防止按钮被压缩的关键设置
   * flex-shrink: 0 - 禁止按钮缩小，保持按钮的最小可读宽度
   * white-space: nowrap - 防止按钮文字换行
   * min-width: 设置按钮最小宽度，确保文字完整显示
   */
  flex-shrink: 0;
  white-space: nowrap;
  min-width: 60px; /* 最小宽度，确保短文字按钮不会太小 */
  max-width: 80px; /* 最大宽度，防止长文字按钮过大 */
}

/* 批量操作栏样式 - 两行布局 */
.batch-actions-bar {
  display: flex;
  /*
   * 布局方向：改为垂直布局（两行）
   * flex-direction: column - 垂直排列子元素
   * 第一行：批量信息文本
   * 第二行：批量操作按钮
   */
  flex-direction: column;

  /*
   * 对齐方式：
   * align-items: stretch - 子元素拉伸填满容器宽度
   * justify-content: flex-start - 子元素从顶部开始排列
   */
  align-items: stretch;
  justify-content: flex-start;

  /*
   * 间距和视觉设计
   * padding: 适中的内边距，为内容提供呼吸空间
   * background-color: 浅蓝色背景，表示批量操作状态
   * border: 蓝色边框，与背景色呼应
   * border-radius: 圆角设计，与页面整体风格一致
   */
  padding: 12px;
  background-color: #e6f3ff;
  border: 1px solid #b3d4ff;
  border-radius: 8px;
  margin-bottom: 12px;

  /*
   * 行间距：两行之间的间距
   * gap: 8px - 第一行文本和第二行按钮之间的间距
   */
  gap: 8px;

  /*
   * 动画效果：从上方滑入
   * 提供良好的视觉反馈，让用户知道批量操作栏已激活
   */
  animation: slide-down 0.3s ease-out;
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.batch-info {
  /*
   * 文字样式设计
   * font-size: 14px - 适中的字体大小，保证可读性
   * color: #0052d9 - 蓝色文字，与操作栏主题色一致
   * font-weight: 500 - 中等字重，突出重要信息但不过于厚重
   */
  font-size: 14px;
  color: #0052d9;
  font-weight: 500;

  /*
   * 第一行布局：文本居中显示
   * text-align: center - 文本居中对齐
   * width: 100% - 占满容器宽度
   * padding: 4px 0 - 上下内边距，增加视觉层次
   */
  text-align: center;
  width: 100%;
  padding: 4px 0;

  /*
   * 文本显示优化
   * white-space: nowrap - 防止文字换行，保持在一行显示
   * overflow: hidden - 防止文本溢出
   * text-overflow: ellipsis - 超长文本显示省略号
   */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.batch-buttons {
  display: flex;
  /*
   * 第二行布局：按钮水平排列并居中
   * justify-content: center - 按钮组居中对齐
   * align-items: center - 按钮垂直居中
   * gap: 8px - 按钮间距，与顶部操作按钮保持一致
   */
  justify-content: center;
  align-items: center;
  gap: 8px;

  /*
   * 容器设置
   * width: 100% - 占满容器宽度
   * flex-wrap: wrap - 允许按钮换行（在极小屏幕上）
   */
  width: 100%;
  flex-wrap: wrap;

  /*
   * 防止按钮溢出的设置
   * overflow-x: auto - 当按钮过多时允许水平滚动
   * min-width: 0 - 允许容器缩小
   */
  overflow-x: auto;
  min-width: 0;

  /*
   * 滚动条样式优化
   * 在小程序中提供平滑的滚动体验
   */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
  scrollbar-width: none; /* Firefox隐藏滚动条 */
  -ms-overflow-style: none; /* IE隐藏滚动条 */
}

/* 隐藏webkit浏览器的滚动条 */
.batch-buttons::-webkit-scrollbar {
  display: none;
}

.batch-buttons .t-button {
  /*
   * 按钮样式：与顶部操作按钮保持一致
   * height: 32px - 与顶部按钮相同的高度
   * padding: 0 12px - 与顶部按钮相同的内边距
   * font-size: 14px - 与顶部按钮相同的字体大小
   */
  height: 32px;
  padding: 0 12px;
  font-size: 14px;

  /*
   * 防止按钮被压缩的关键设置
   * flex-shrink: 0 - 禁止按钮缩小，保持按钮的最小可读宽度
   * white-space: nowrap - 防止按钮文字换行
   * min-width: 60px - 与顶部按钮相同的最小宽度
   * max-width: 80px - 与顶部按钮相同的最大宽度
   */
  flex-shrink: 0;
  white-space: nowrap;
  min-width: 60px;
  max-width: 80px;
}

/* 批量模式下的卡片样式 */
.course-card.batch-mode {
  border: 2px solid #0052d9;
  box-shadow: 0 4px 12px rgba(0, 82, 217, 0.2);
  transform: translateY(-2px);
}

.course-title-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  overflow: hidden;
}

.course-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.custom-checkbox {
  width: 18px;
  height: 18px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.custom-checkbox.checked {
  background-color: #0052d9;
  border-color: #0052d9;
}

.check-icon {
  color: #ffffff;
}

/* 移除学员确认对话框样式 */
.remove-student-dialog-content {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.remove-student-dialog-content text {
  display: block;
  margin-bottom: 8px;
}

.remove-student-dialog-content .student-name {
  font-weight: bold;
  color: #333;
}

.refund-checkbox-container {
  display: flex;
  align-items: center;
  margin-top: 16px;
}

.refund-checkbox {
  width: 18px;
  height: 18px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  transition: all 0.2s ease;
}

.refund-checkbox.checked {
  background-color: #0052d9;
  border-color: #0052d9;
}

.refund-checkbox .check-icon {
  color: #ffffff;
}

.refund-label {
  font-size: 14px;
  color: #333;
}

/* 倒计时确认对话框样式 */
.countdown-dialog-content {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.countdown-dialog-content text {
  display: block;
  margin-bottom: 8px;
}

.countdown-dialog-content .course-name {
  font-weight: bold;
  color: #d9534f;
}

/* 加载指示器样式 */
.loading-indicator {
  text-align: center;
  color: #888;
  font-size: 14px;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.loading-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #0052d9;
  animation: loading-dot-bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loading-dot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 到底部提示样式 */
.end-indicator {
  text-align: center;
  color: #b0b0b0;
  font-size: 14px;
  padding: 16px 0;
  letter-spacing: 1px;
}

/* 自定义下拉刷新样式 */
.custom-refresher {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 45px;
  background-color: #f5f5f5;
}

.refresher-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresher-icon {
  font-size: 16px;
  color: #666;
  transition: transform 0.2s ease;
}

.refresher-text {
  font-size: 14px;
  color: #666;
}

/* 回到顶部按钮样式 */
.back-to-today-fab {
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  z-index: 100;
}

.back-to-today-fab .t-fab__btn {
  background-color: rgba(0, 0, 0, 0.5) !important;
  color: #ffffff !important;
}

/* 响应式布局调整 */
/* 针对小屏幕进行优化，确保标签栏和按钮在不同尺寸下都能正确显示 */

/* 中等小屏幕优化 (iPhone SE, 小屏Android) */
@media (max-width: 414px) {
  .actions-container {
    /*
     * 在中等小屏幕上进一步限制按钮容器宽度
     * 为搜索图标和间距留出更多空间
     */
    max-width: 65vw;
    gap: 6px; /* 减小按钮间距 */
  }

  .actions-container .t-button {
    /*
     * 优化按钮尺寸以适应中等小屏幕
     * 减小内边距和字体大小，但保持可读性
     */
    padding: 0 10px;
    font-size: 13px;
    min-width: 55px; /* 稍微减小最小宽度 */
    max-width: 75px;
  }

  /* 批量操作栏在中等小屏幕的优化 - 两行布局 */
  .batch-actions-bar {
    /*
     * 在中等小屏幕上减小内边距，为内容腾出更多空间
     * 保持两行布局的间距
     */
    padding: 10px;
    gap: 6px; /* 减小行间距 */
  }

  .batch-info {
    /*
     * 优化批量信息文字显示
     * 减小字体但保持可读性
     */
    font-size: 13px;
    padding: 2px 0; /* 减小上下内边距 */
  }

  .batch-buttons {
    gap: 6px; /* 减小按钮间距 */
  }

  .batch-buttons .t-button {
    /*
     * 批量操作按钮在中等小屏幕的优化
     * 与顶部按钮保持一致的响应式调整
     */
    height: 30px; /* 稍微减小高度 */
    padding: 0 10px;
    font-size: 13px;
    min-width: 55px;
    max-width: 75px;
  }
}

/* 小屏幕优化 (iPhone SE第一代等) */
@media (max-width: 390px) {
  .custom-top-tabs .t-tabs__item {
    padding: 12px 14px !important; /* 减小水平内边距 */
    font-size: 15px !important; /* 缩小字体 */
    min-height: 42px; /* 减小最小高度 */
  }

  .actions-container {
    /*
     * 在小屏幕上进一步优化按钮容器
     * 确保即使是最长的按钮文字也能完整显示
     */
    max-width: 60vw;
    gap: 4px; /* 进一步减小间距 */
  }

  .actions-container .t-button {
    /*
     * 小屏幕下的按钮优化
     * 平衡可读性和空间利用率
     */
    padding: 0 8px;
    font-size: 12px;
    min-width: 50px;
    max-width: 70px;
  }

  /* 批量操作栏在小屏幕的优化 - 两行布局 */
  .batch-actions-bar {
    /*
     * 在小屏幕上进一步减小内边距
     * 最大化可用空间，保持两行布局
     */
    padding: 8px;
    gap: 5px; /* 进一步减小行间距 */
  }

  .batch-info {
    /*
     * 小屏幕下的批量信息优化
     * 进一步减小字体
     */
    font-size: 12px;
    padding: 1px 0; /* 减小上下内边距 */
  }

  .batch-buttons {
    gap: 4px; /* 减小按钮间距 */
  }

  .batch-buttons .t-button {
    /*
     * 小屏幕下的批量操作按钮优化
     * 与顶部按钮保持一致的响应式调整
     */
    height: 28px; /* 稍微减小高度 */
    padding: 0 8px;
    font-size: 12px;
    min-width: 50px;
    max-width: 70px;
  }
}

/* 超小屏幕优化 (iPhone 5/SE第一代等) */
@media (max-width: 375px) {
  .container {
    padding: 8px;
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
  }

  .course-card,
  .template-card {
    padding: 12px;
  }

  .course-title,
  .template-title {
    font-size: 15px;
  }

  .info-item {
    font-size: 13px;
  }

  .booking-tab {
    font-size: 13px;
    padding: 6px 4px;
  }

  .actions-container {
    /*
     * 超小屏幕的极限优化
     * 确保在最小的屏幕上也能正常显示
     */
    max-width: 55vw;
    gap: 3px; /* 最小间距 */
  }

  .actions-container .t-button {
    /*
     * 超小屏幕下的按钮设置
     * 在保持可用性的前提下最大化空间利用
     */
    font-size: 11px;
    padding: 0 6px;
    min-width: 45px;
    max-width: 65px;
    height: 30px; /* 稍微减小高度 */
  }

  /* 批量操作栏在超小屏幕的极限优化 - 两行布局 */
  .batch-actions-bar {
    /*
     * 超小屏幕的极限优化
     * 最小化内边距，最大化可用空间，保持两行布局
     */
    padding: 6px;
    gap: 4px; /* 最小行间距 */
  }

  .batch-info {
    /*
     * 超小屏幕下的批量信息极限优化
     * 保持信息可读的前提下最小化占用空间
     */
    font-size: 11px;
    padding: 1px 0; /* 最小上下内边距 */
  }

  .batch-buttons {
    gap: 3px; /* 最小按钮间距 */
  }

  .batch-buttons .t-button {
    /*
     * 超小屏幕下的批量操作按钮极限优化
     * 与顶部按钮保持一致的响应式调整
     */
    height: 26px; /* 稍微减小高度 */
    padding: 0 6px;
    font-size: 11px;
    min-width: 45px;
    max-width: 65px;
  }

  .custom-top-tabs .t-tabs__item {
    padding: 12px 12px !important; /* 进一步减小内边距 */
    font-size: 15px !important;
  }
}