<!--profile.wxml-->
<!--
  个人中心页面结构文件
  这是小程序的个人中心页面，负责展示用户信息和功能导航

  页面功能：
  1. 未登录状态：显示登录界面，包含Logo、登录按钮、用户协议
  2. 已登录状态：显示用户信息和功能菜单（学员、讲师、管理员功能）

  页面结构类似于：
  - Web应用的用户中心页面
  - 移动应用的"我的"页面
  - 企业应用的个人工作台
-->

<!-- 根容器 -->
<view class="container">
  <!-- 装饰性浮动元素 -->
  <view class="floating-decorations">
    <view class="floating-dot dot-1"></view>
    <view class="floating-dot dot-2"></view>
    <view class="floating-dot dot-3"></view>
    <view class="floating-dot dot-4"></view>
  </view>
  <!--
    未登录状态显示区域
    使用条件渲染，只有当用户未登录时才显示

    wx:if条件渲染说明：
    - {{!isLoggedIn}}: 感叹号(!)表示逻辑非运算
    - 当isLoggedIn为false时，!isLoggedIn为true，显示登录界面
    - 当isLoggedIn为true时，!isLoggedIn为false，隐藏登录界面

    与其他技术对比：
    - Vue: v-if="!isLoggedIn"
    - React: {!isLoggedIn && <div>...</div>}
    - Angular: *ngIf="!isLoggedIn"
  -->
  <view class="login-section" wx:if="{{!isLoggedIn}}">
    <!-- 登录卡片容器 -->
    <view class="login-card">
      <!-- 登录内容区域 -->
      <view class="login-content">
        <!--
          应用Logo图片
          t-image: TDesign的图片组件，功能比原生image更丰富

          属性说明：
          1. src: 图片源地址（云存储地址）
          2. mode: 图片显示模式
             - aspectFill: 保持比例填满容器，可能裁剪部分内容
             - 其他选项：contain(完整显示)、cover(填满容器)、widthFix(宽度固定)等
          3. class: CSS类名，用于样式控制
          4. width/height: 图片尺寸，使用rpx单位（响应式像素）
          5. shape: 图片形状，round表示圆角
          6. binderror: 图片加载失败时的事件处理函数

          rpx单位说明：
          - rpx是小程序的响应式像素单位
          - 规定屏幕宽度为750rpx，会根据实际屏幕宽度自动缩放
          - iPhone6屏幕宽度375px = 750rpx，即1px = 2rpx
          - 200rpx在iPhone6上显示为100px
        -->
        <t-image
          src="cloud://cloud1-1gm190n779af8083.636c-cloud1-1gm190n779af8083-1365450081/logo/logo.jpg"
          mode="aspectFill"
          class="logo"
          width="200rpx"
          height="200rpx"
          shape="round"
          binderror="onAvatarError"
        />

        <!-- 欢迎文字 -->
        <view class="login-text">欢迎使用伽House</view>

        <!-- 提示文字 -->
        <view class="login-tip">登录后即可享受完整的约课服务</view>

        <!--
          微信一键登录按钮区域
          包含登录按钮和用户协议勾选
        -->
        <view class="login-button-section">
          <!--
            登录按钮
            t-button: TDesign的按钮组件

            属性说明：
            1. theme: 按钮主题样式
               - primary: 主要按钮（蓝色背景）
               - secondary: 次要按钮（灰色边框）
               - danger: 危险按钮（红色背景）
            2. size: 按钮尺寸
               - small: 小尺寸
               - medium: 中等尺寸（默认）
               - large: 大尺寸
            3. block: 块级按钮，占满父容器宽度
            4. bind:tap: 点击事件绑定，点击时调用handleWxLogin方法
            5. class: CSS类名，用于自定义样式

            按钮内容：
            - t-icon: 微信图标，增强视觉效果
            - 文字: "微信一键登录"
          -->
          <t-button
            theme="primary"
            size="large"
            block
            bind:tap="handleWxLogin"
            class="wx-login-btn"
          >
            <!--
              微信图标
              t-icon: TDesign的图标组件
              - name="wechat": 微信图标
              - size="20": 图标大小20px
              - style: 内联样式，设置右边距
              - color="#07C160": 微信绿色
            -->
            <t-icon name="wechat" size="20" style="margin-right: 8px;" color="#07C160" />
            微信一键登录
          </t-button>
        </view>

        <!--
          用户协议勾选区域
          用户必须勾选同意协议才能登录，这是法律合规要求
        -->
        <view class="agreement-section">
          <!--
            复选框组件
            t-checkbox: TDesign的复选框组件

            属性说明：
            1. checked: 绑定勾选状态，双向数据绑定
               - {{agreementChecked}}: 绑定到JS中的agreementChecked变量
            2. bind:change: 状态改变时的事件处理函数
               - 当用户点击复选框时，调用onAgreementChange方法

            复选框内容：
            - 包含普通文字和可点击的协议链接
            - 点击协议链接可以查看完整协议内容
          -->
          <t-checkbox checked="{{agreementChecked}}" bind:change="onAgreementChange">
            <text>我已阅读并同意</text>
            <!--
              协议链接
              text组件 + bind:tap事件 + CSS样式 = 可点击的链接效果
              点击时调用showAgreement方法显示协议内容
            -->
            <text class="agreement-link" bind:tap="showAgreement">《伽House小程序用户协议》</text>
          </t-checkbox>
        </view>
      </view>
    </view>
  </view>

  <!--
    已登录状态显示区域
    使用wx:else与上面的wx:if形成条件分支

    wx:else说明：
    - 与wx:if配对使用，类似于编程语言的if-else语句
    - 当wx:if条件为false时，wx:else部分会显示
    - 这里表示：当用户已登录时，显示个人中心界面
  -->
  <view class="profile-section" wx:else>
    <!--
      用户信息卡片
      显示用户的基本信息：头像、昵称、角色等

      CSS类名说明：
      - profile-card: 基础卡片样式
      - info: 信息类卡片的特殊样式（通常有特定的颜色标识）
      - card-animate: 添加入场动画效果
    -->
    <view class="profile-card info card-animate">
      <!--
        卡片头部
        包含图标和标题，统一的卡片头部设计
      -->
      <view class="card-header">
        <view class="card-header-left">
          <!-- 用户图标，绿色主题 -->
          <t-icon name="user" size="24" color="#36B37E" />
          <!-- 卡片标题 -->
          <text class="card-title">用户信息</text>
        </view>

        <!-- 通知图标区域 -->
        <view class="notification-icon-wrapper" bind:tap="goToNotifications">
          <!--
            通知图标
            t-icon: TDesign的图标组件
            - name="mail": 邮件/消息图标
            - size="24": 图标大小24px
            - color="#666666": 灰色图标
          -->
          <t-icon name="mail" size="24" color="#666666" />

          <!--
            未读消息红点
            只有当有未读消息时才显示

            条件渲染：wx:if="{{unreadCount > 0}}"
            当未读消息数量大于0时显示红点
          -->
          <view class="notification-badge" wx:if="{{unreadCount > 0}}">
            <!--
              未读数量显示
              如果未读数量小于等于99，显示具体数字
              如果超过99，显示"99+"
            -->
            <text class="badge-text">{{unreadCount > 99 ? '99+' : unreadCount}}</text>
          </view>
        </view>
      </view>

      <!--
        卡片内容区域
        包含用户的详细信息展示
      -->
      <view class="card-content">
        <!--
          用户信息展示区域
          采用左右布局：左侧头像，右侧用户详情
        -->
        <view class="user-info">
          <!--
            头像区域包装器
            支持点击更换头像功能
          -->
          <view class="avatar-wrapper">
            <!--
              用户头像图片
              支持点击更换头像，显示默认头像兜底

              属性说明：
              1. src: 头像地址，使用逻辑或(||)运算符提供默认值
                 - {{userInfo.avatarUrl || '默认头像地址'}}
                 - 如果用户有自定义头像则显示自定义头像
                 - 如果没有则显示默认的Logo作为头像
              2. mode="aspectFill": 保持比例填满容器
              3. width/height="80": 头像尺寸80px
              4. shape="round": 圆形头像
              5. binderror: 头像加载失败时的处理函数
            -->
            <t-image
              src="{{userInfo.avatarUrl || 'cloud://cloud1-1gm190n779af8083.636c-cloud1-1gm190n779af8083-1365450081/logo/logo.jpg'}}"
              mode="aspectFill"
              class="avatar"
              width="80"
              height="80"
              shape="round"
              binderror="onAvatarError"
            />

            <!--
              头像更换按钮（透明覆盖层）
              button组件的特殊用法：

              1. open-type="chooseAvatar": 小程序提供的特殊按钮类型
                 - 点击时会调起微信的头像选择器
                 - 用户可以从相册选择或拍照设置头像
                 - 这是微信小程序的原生能力

              2. bind:chooseavatar: 头像选择完成后的回调事件
                 - 当用户选择头像后，会调用onChooseAvatar方法
                 - 方法会接收到选择的头像信息

              3. class="avatar-btn-overlay": CSS类名
                 - 通过CSS设置为透明覆盖层
                 - 覆盖在头像上方，用户点击头像实际是点击这个按钮
                 - 这样既保持了视觉效果，又实现了功能

              技术原理：
              - 类似于Web开发中的文件上传按钮美化
              - 将原生按钮设为透明，覆盖在美化后的元素上
              - 用户看到的是美化后的界面，点击的是功能按钮
            -->
            <button class="avatar-btn-overlay" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar"></button>
          </view>

          <!--
            用户详细信息区域
            包含用户名和角色标签
          -->
          <view class="user-details">
            <!--
              用户昵称显示
              使用逻辑或(||)提供默认值，防止显示空白
            -->
            <view class="user-name">{{userInfo.nickName || '微信用户'}}</view>

            <!--
              用户角色标签区域
              支持多角色显示，每个角色显示为一个标签
            -->
            <view class="user-role">
              <!--
                角色标签列表渲染
                wx:for遍历roles数组，为每个角色创建一个标签

                t-tag: TDesign的标签组件
                属性说明：
                1. wx:for="{{roles}}": 遍历角色数组
                2. wx:key="index": 使用索引作为唯一标识
                3. theme="primary": 主题色标签（蓝色）
                4. variant="light": 浅色变体（浅蓝色背景）
                5. size="small": 小尺寸标签
                6. style: 内联样式，设置右边距分隔标签
                7. {{item}}: 显示当前角色名称

                效果示例：
                - 如果用户角色是['学员', '讲师']
                - 会显示两个标签：[学员] [讲师]
              -->
              <t-tag
                wx:for="{{roles}}"
                wx:key="index"
                theme="primary"
                variant="light"
                size="small"
                style="margin-right:8rpx;"
              >
                {{item}}
              </t-tag>
            </view>
          </view>
        </view>
      </view>
    </view>



    <!--
      学员功能卡片
      只有当用户具有学员角色时才显示

      条件渲染：wx:if="{{showStudentGroup}}"
      - showStudentGroup在JS中根据用户角色计算得出
      - 只有学员才能看到学员专属功能
    -->
    <view class="profile-card student" wx:if="{{showStudentGroup}}">
      <!--
        卡片头部
        包含学员功能的图标和标题
      -->
      <view class="card-header">
        <view class="card-header-left">
          <!--
            学员图标
            - name="user-add": 用户添加图标，表示学员身份
            - color="#FFAA00": 橙色主题，区分不同角色
          -->
          <t-icon name="user-add" size="24" color="#FFAA00" />
          <text class="card-title">学员功能</text>
        </view>
      </view>

      <!--
        卡片内容区域
        包含学员可以使用的功能列表
      -->
      <view class="card-content">
        <!--
          功能列表容器
          采用垂直排列的列表布局
        -->
        <view class="function-list">
          <!--
            我的活动功能项
            点击跳转到我的预约页面，查看已预约的课程
          -->
          <view class="function-item" bind:tap="goToMyBookings">
            <!-- 日历图标，表示活动/课程 -->
            <t-icon name="calendar" size="24" color="#0052D9" />
            <text class="function-item-label">我的活动</text>
            <!-- 右箭头图标，表示可以点击进入 -->
            <t-icon name="chevron-right" size="16" />
          </view>

          <!--
            考勤卡功能项
            点击跳转到考勤卡页面，查看打卡记录
          -->
          <view class="function-item" bind:tap="goToMembershipCard">
            <!-- 信用卡图标，表示考勤卡 -->
            <t-icon name="creditcard" size="24" color="#FF6F00" />
            <text class="function-item-label">考勤卡</text>
            <t-icon name="chevron-right" size="16" />
          </view>
        </view>
      </view>
    </view>

    <!--
      讲师功能卡片
      只有当用户具有讲师角色时才显示

      条件渲染：wx:if="{{showCoachGroup}}"
      - showCoachGroup在JS中根据用户角色计算得出
      - 只有讲师才能看到讲师专属功能
    -->
    <view class="profile-card coach" wx:if="{{showCoachGroup}}">
      <!--
        卡片头部
        包含讲师功能的图标和标题
      -->
      <view class="card-header">
        <!--
          讲师图标
          - name="user-talk": 用户对话图标，表示讲师身份
          - color="#36B37E": 绿色主题，区分不同角色
        -->
        <t-icon name="user-talk" size="24" color="#36B37E" />
        <text class="card-title">讲师功能</text>
      </view>

      <!--
        卡片内容区域
        包含讲师可以使用的功能列表
      -->
      <view class="card-content">
        <!--
          功能列表容器
          采用垂直排列的列表布局
        -->
        <view class="function-list">
          <!--
            我的课表功能项
            点击跳转到讲师课表页面，查看和管理自己的课程
          -->
          <view class="function-item" bind:tap="goToCoachSchedule">
            <!-- 日历图标，表示课表/课程安排 -->
            <t-icon name="calendar" size="24" color="#0052D9" />
            <text class="function-item-label">我的课表</text>
            <!-- 右箭头图标，表示可以点击进入 -->
            <t-icon name="chevron-right" size="16" />
          </view>
        </view>
      </view>
    </view>

    <!--
      管理员功能卡片
      只有当用户具有管理员角色时才显示

      条件渲染：wx:if="{{showAdminGroup}}"
      - showAdminGroup在JS中根据用户角色计算得出
      - 只有管理员才能看到管理员专属功能
      - 管理员功能最多，包含系统的核心管理能力
    -->
    <view class="profile-card admin" wx:if="{{showAdminGroup}}">
      <!--
        卡片头部
        包含管理功能的图标和标题
      -->
      <view class="card-header">
        <!--
          设置图标
          - name="setting": 设置图标，表示管理功能
          - color="#0052D9": 蓝色主题，表示重要的管理功能
        -->
        <t-icon name="setting" size="24" color="#0052D9" />
        <text class="card-title">管理功能</text>
      </view>

      <!--
        卡片内容区域
        包含管理员可以使用的所有管理功能
      -->
      <view class="card-content">
        <!--
          功能列表容器
          管理员功能较多，采用垂直排列的列表布局
        -->
        <view class="function-list">
          <!--
            用户管理功能项
            点击跳转到用户管理页面，管理所有用户的角色和权限
          -->
          <view class="function-item" bind:tap="goToUserManagement">
            <!-- 用户图标，表示用户管理 -->
            <t-icon name="user" size="24" color="#36B37E" />
            <text class="function-item-label">用户管理</text>
            <t-icon name="chevron-right" size="16" />
          </view>

          <!--
            活动管理功能项
            点击跳转到课程管理页面，创建、编辑、删除课程
          -->
          <view class="function-item" bind:tap="goToCourseManagement">
            <!-- 编辑图标，表示内容管理 -->
            <t-icon name="edit" size="24" color="#FFAA00" />
            <text class="function-item-label">活动管理</text>
            <t-icon name="chevron-right" size="16" />
          </view>

          <!--
            考勤卡管理功能项
            点击跳转到考勤卡管理页面，管理所有用户的考勤记录
          -->
          <view class="function-item" bind:tap="goToMembershipCardManagement">
            <!-- 信用卡图标，表示考勤卡管理 -->
            <t-icon name="creditcard" size="24" color="#FF6F00" />
            <text class="function-item-label">考勤卡管理</text>
            <t-icon name="chevron-right" size="16" />
          </view>

          <!--
            系统设置功能项
            点击跳转到系统设置页面，配置系统参数和规则
          -->
          <view class="function-item" bind:tap="goToSystemSettings">
            <!-- 设置图标，表示系统配置 -->
            <t-icon name="setting" size="24" color="#0052D9" />
            <text class="function-item-label">系统设置</text>
            <t-icon name="chevron-right" size="16" />
          </view>

          <!--
            相册管理功能项
            点击跳转到相册管理页面，管理首页展示的图片
          -->
          <view class="function-item" bind:tap="onAdminAlbumTap">
            <!-- 图片图标，表示相册/图片管理 -->
            <t-icon name="image" size="24" color="#00B8D9" />
            <text class="function-item-label">相册管理</text>
            <t-icon name="chevron-right" size="16" />
          </view>
        </view>
      </view>
    </view>

    <!--
      设置与帮助卡片
      所有已登录用户都可以看到的通用功能

      无条件渲染：
      - 不需要wx:if条件，所有登录用户都能看到
      - 包含个人设置、帮助信息、退出登录等基础功能
      - 这些功能不受角色限制，是用户的基本权利
    -->
    <view class="profile-card help">
      <!--
        卡片头部
        包含设置功能的图标和标题
      -->
      <view class="card-header">
        <!--
          设置图标
          - name="setting": 设置图标，表示设置和帮助功能
          - color="#0052D9": 蓝色主题，与管理功能保持一致
        -->
        <t-icon name="setting" size="24" color="#0052D9" />
        <text class="card-title">设置与帮助</text>
      </view>

      <!--
        卡片内容区域
        包含所有用户都可以使用的基础功能
      -->
      <view class="card-content">
        <!--
          功能列表容器
          采用垂直排列的列表布局
        -->
        <view class="function-list">
          <!--
            个人资料功能项
            点击跳转到个人资料编辑页面，修改个人信息
          -->
          <view class="function-item" bind:tap="goToProfile">
            <!-- 用户图标，表示个人信息 -->
            <t-icon name="user" size="24" color="#36B37E" />
            <text class="function-item-label">个人资料</text>
            <t-icon name="chevron-right" size="16" />
          </view>

          <!--
            关于功能项
            点击显示关于小程序的信息，包括版本号、联系方式等
          -->
          <view class="function-item" bind:tap="goToAbout">
            <!-- 帮助圆圈图标，表示帮助和关于信息 -->
            <t-icon name="help-circle" size="24" color="#00B8D9" />
            <text class="function-item-label">关于</text>
            <t-icon name="chevron-right" size="16" />
          </view>

          <!--
            退出登录功能项
            点击退出当前用户登录，返回到登录界面

            特殊样式：
            - 使用红色主题，表示这是一个重要的操作
            - 文字也设置为红色，与图标保持一致
            - 提醒用户这是一个需要谨慎操作的功能
          -->
          <view class="function-item" bind:tap="logout">
            <!-- 退出图标，红色主题表示重要操作 -->
            <t-icon name="logout" size="24" color="#e34d59" />
            <!-- 退出登录文字，也设置为红色 -->
            <text class="function-item-label" style="color:#e34d59;">退出登录</text>
            <t-icon name="chevron-right" size="16" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!--
    Toast消息提示组件
    TDesign的消息提示组件，用于显示操作反馈

    组件说明：
    1. id="t-toast": 组件唯一标识符
       - 在JS中通过this.selectComponent('#t-toast')获取组件实例
       - 用于调用showToast、hideToast等方法

    2. 全局消息提示：
       - 成功提示：操作成功时显示绿色提示
       - 错误提示：操作失败时显示红色提示
       - 警告提示：需要用户注意时显示橙色提示
       - 信息提示：一般信息时显示蓝色提示

    3. 使用场景：
       - 登录成功/失败提示
       - 头像上传成功/失败提示
       - 退出登录确认提示
       - 网络请求错误提示
       - 其他用户操作反馈

    4. 技术特点：
       - 自动消失：默认3秒后自动隐藏
       - 位置固定：显示在屏幕顶部或中央
       - 层级最高：覆盖在所有内容之上
       - 响应式：适配不同屏幕尺寸

    与其他技术对比：
    - Web开发：类似于alert()、notification API
    - Vue：类似于this.$message()
    - React：类似于antd的message组件
    - 原生小程序：类似于wx.showToast()，但功能更丰富
  -->
  <t-toast id="t-toast" />

  <!--
    新版用户协议弹窗
    使用TDesign的t-dialog组件实现，具有更好的UI效果和交互体验

    组件属性说明：
    1. visible: 控制弹窗的显示和隐藏，绑定到JS中的showAgreementDialog变量
    2. title: 弹窗标题，绑定到JS中的agreementTitle变量
    3. show-overlay: 是否显示遮罩层，提供模态效果
    4. close-on-overlay-click: 点击遮罩层是否可以关闭弹窗
    5. confirm-btn: 确认按钮的文本和主题
    6. cancel-btn: 隐藏取消按钮
    7. bind:confirm: 点击确认按钮时的回调事件，调用hideAgreementDialog方法
    8. bind:close: 点击关闭按钮或遮罩层时的回调事件
  -->
  <t-dialog
    visible="{{showAgreementDialog}}"
    title="{{agreementTitle}}"
    show-overlay="true"
    close-on-overlay-click="true"
    confirm-btn="我已知晓"
    cancel-btn=""
    bind:confirm="hideAgreementDialog"
    bind:close="hideAgreementDialog"
    class="agreement-dialog"
  >
    <!--
      弹窗内容区域
      使用scroll-view实现可滚动的协议内容
    -->
    <scroll-view scroll-y="true" class="agreement-content-scroll">
      <!--
        遍历协议内容数组
        wx:for遍历JS中处理好的agreementContent数组
        每个段落包含标题和正文
      -->
      <view wx:for="{{agreementContent}}" wx:key="index" class="agreement-paragraph">
        <!-- 协议段落标题 -->
        <view class="agreement-paragraph-title">{{item.title}}</view>
        <!-- 协议段落正文 -->
        <view class="agreement-paragraph-body">{{item.body}}</view>
      </view>
    </scroll-view>
  </t-dialog>
</view>