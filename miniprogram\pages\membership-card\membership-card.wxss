/* 页面容器样式 - 简洁干净 */
.membership-card-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
  padding-top: 20rpx;
}

/* 统计信息卡片样式 */
.statistics-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  margin: 0 32rpx 20rpx 32rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.stats-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #0052d9;
  margin-bottom: 8rpx;
}

.stat-number.warning {
  color: #ff9500;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  font-weight: 500;
}

/* 卡片列表容器 */
.card-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin: 0 32rpx;
  padding-top: 20rpx;
}

/* 考勤卡片样式 - 简洁卡片设计 */
.membership-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  padding: 32rpx 24rpx;
  position: relative;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  transition: all 0.2s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}
/* 卡片点击效果 */
.membership-card:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 卡片图标样式 */
.card-header t-icon {
  color: #0052d9;
  margin-right: 12rpx;
}

/* 卡号样式 */
.card-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

/* 状态标签样式 */
.card-header t-tag {
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  font-size: 22rpx;
  font-weight: 500;
  border-radius: 8rpx;
  flex-shrink: 0;
  white-space: nowrap;
}

/* 卡片主体内容样式 */
.card-body {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

/* 信息行样式 */
.card-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #666;
  padding: 8rpx 0;
}

/* 最后一行不显示边距 */
.card-row:last-child {
  margin-bottom: 0;
}

/* 标签文字样式 */
.card-row text:first-child,
.card-row .label {
  color: #999;
  font-weight: 500;
  font-size: 26rpx;
  min-width: 120rpx;
}

/* 数值文字样式 */
.card-row text:last-child,
.card-row .value {
  color: #333;
  font-weight: 500;
  font-size: 28rpx;
  text-align: right;
  flex: 1;
}

/* 重要数值高亮样式 */
.card-row .value.highlight {
  color: #0052d9;
  font-weight: 600;
  font-size: 30rpx;
}
/* 空状态样式 */
.membership-card-page t-empty {
  margin-top: 120rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 60rpx 40rpx;
  margin-left: 32rpx;
  margin-right: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* TabBar占位符 */
#tab-bar-placeholder {
  height: 120rpx;
}