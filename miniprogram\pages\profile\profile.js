// profile.js
// 个人中心页面逻辑文件
// 这是小程序的个人中心页面，负责用户登录、个人信息展示、功能导航等核心功能
// 类似于Web应用的用户中心页面或移动应用的"我的"页面

/**
 * 模块导入说明
 *
 * ES6模块导入语法：import { 具体函数名 } from '模块路径'
 * 这种方式只导入需要的函数，有助于减少代码体积（Tree Shaking）
 *
 * 与其他技术对比：
 * - Java: import java.util.List;
 * - C#: using System.Collections.Generic;
 * - Python: from utils import showToast
 * - Node.js: const { showToast } = require('./utils/toast.js');
 */

// 导入Toast工具函数，用于显示消息提示
// 这些函数封装了小程序的消息提示逻辑，提供统一的用户反馈体验
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

const agreementParagraphs = [
  `《伽House小程序用户协议》\n\n欢迎您使用伽House小程序（以下简称“本小程序”）。在使用本小程序前，请您仔细阅读并充分理解本协议的全部内容，特别是免除或限制责任的条款。您一旦注册、登录、使用本小程序，即视为已阅读并同意本协议的全部内容。`,
  `特别声明：伽House小程序仅为一个小工具，仅供组织小范围的非商业活动使用。伽House的运营人并非本小程序的开发者。本小程序由开发者基于友情支持为伽House实际控制人开发，所有功能、规则、内容均由伽House实际控制人提出、制定并负责解释。小程序开发者未收取任何开发报酬，仅按伽House实际控制人要求提供开发服务。小程序开发者不参与运营管理，不对小程序的使用、内容、规则、服务及由此产生的任何后果承担任何法律或经济责任。如有疑问、争议或解释权，均归伽House实际控制人所有。本小程序及伽House组织的所有活动均为非商业性质，不涉及任何商业交易、盈利或收费行为。`,
  `一、服务内容\n1. 本小程序为用户提供活动预约、日程查看、用户信息管理等基础服务，支持普通成员、活动组织者、管理员三种角色。\n2. 本小程序基于微信云开发平台，用户需通过微信一键登录方可使用全部功能。\n3. 本小程序部分功能可能会根据实际需求进行调整或扩展，具体以实际页面展示为准。`,
  `二、用户注册与使用\n1. 用户需使用微信账号授权登录，系统将获取您的微信头像、昵称等基础信息用于身份识别和展示。\n2. 新注册用户默认角色为“普通成员”，如需成为活动组织者或管理员，需由管理员审核分配。\n3. 用户应保证所提交信息真实、准确、完整，如信息变更请及时更新。\n4. 用户应妥善保管个人微信账号及相关信息，因账号泄露造成的损失由用户自行承担。`,
  `三、用户权利与义务\n1. 用户有权根据自身角色使用相应功能，包括但不限于活动预约、取消、查看日程、管理活动等。\n2. 用户应遵守伽House相关规定，合理预约活动，不得恶意占用资源或扰乱正常组织秩序。\n3. 用户不得利用本小程序从事任何违法违规活动，不得上传、传播违法或不良信息。\n4. 用户应尊重其他用户的合法权益，不得进行骚扰、侮辱、诽谤等行为。`,
  `四、活动预约与取消\n1. 普通成员可在活动剩余名额范围内预约活动，预约成功后可在“我的活动”中查看。\n2. 如需取消预约，须在活动开始前操作，逾期将无法取消。\n3. 预约记录将保留，供用户和管理员查询。\n4. 如遇特殊情况（如不可抗力、活动变动等），伽House有权对预约进行调整或取消，并通过小程序公告或消息通知用户。`,
  `五、信息保护与隐私政策\n1. 本小程序严格遵守国家相关法律法规，保护用户个人信息安全。\n2. 用户信息仅用于活动预约、身份识别、服务优化等与本小程序相关的用途，未经用户同意不会向第三方披露。\n3. 如因不可抗力或非本小程序原因导致信息泄露、丢失、被盗用等，本小程序将协助用户妥善处理，但不承担由此产生的法律责任。\n4. 用户有权随时查询、更正或删除自己的个人信息，具体操作可通过“个人资料”页面或联系客服实现。`,
  `六、服务变更、中断与终止\n1. 本小程序有权根据实际需要随时变更、暂停或终止部分或全部服务，并提前通过公告等方式通知用户。\n2. 如用户违反本协议或相关规定，本小程序有权暂停或终止其使用资格。\n3. 如因不可抗力、政策调整、技术升级等原因导致服务中断或终止，本小程序将尽力提前通知用户并妥善处理相关事宜。`,
  `七、免责声明及异常情况处理\n1. 本小程序致力于提供安全、稳定的服务，但不保证服务不会中断或无错误。\n2. 因网络、设备故障、第三方原因等导致的服务中断或数据丢失，本小程序不承担赔偿责任。\n3. 用户因自身原因造成的损失，由用户自行承担。\n4. 伽House实际控制人对本小程序的运营、内容、规则、服务等承担全部解释权和责任。小程序开发者仅提供技术开发支持，不对本小程序的任何运营、内容、规则、服务及由此产生的任何后果承担任何法律或经济责任。\n5. 由于程序可能存在缺陷或不可预见的问题，可能导致用户预约失败、信息出错或其他异常情况。对于因此产生的用户不当收益，伽House的运营人有权向用户追回相关收益。对于故意利用或滥用程序漏洞（bug）牟利的用户，伽House将坚决追究其法律责任，包括但不限于追究刑事责任。\n6. 用户如发现系统漏洞或异常情况，有义务及时反馈给伽House工作人员，不得恶意传播或利用。`,
  `八、知识产权声明\n1. 本小程序及其内容（包括但不限于界面设计、图标、文字、图片、代码等）均受相关法律保护。小程序的全部版权归开发者所有，伽House仅拥有小程序的使用权和运营权。\n2. 小程序的源代码及整体著作权归开发者所有，伽House仅拥有小程序的使用权和运营权，未经开发者书面许可，任何单位和个人不得擅自复制、传播、修改、反编译、商用或以其他方式使用本小程序的全部或部分代码。\n3. 未经授权，任何单位和个人不得擅自复制、传播、修改、使用本小程序内容。\n4. 如需商业合作或内容授权，请联系伽House实际控制人及开发者。`,
  `九、协议修改\n1. 本小程序有权根据实际情况对本协议内容进行修改，修改后的协议将通过小程序公告等方式通知用户。\n2. 用户如不同意修改内容，应立即停止使用本小程序，继续使用视为接受修改后的协议。`,
  `十、法律适用与争议解决\n1. 本协议的订立、执行与解释及争议的解决均适用中华人民共和国法律。\n2. 因本协议产生的争议，双方应友好协商解决，协商不成时，任何一方可向伽House所在地有管辖权的人民法院提起诉讼。`,
  `十一、未成年人保护\n1. 未满18周岁的未成年人应在监护人指导下使用本小程序。\n2. 伽House将根据国家相关法律法规保护未成年人合法权益，若发现未成年人违规使用，将有权采取限制措施。`,
  `十二、其他\n1. 本协议部分条款无效或不可执行，不影响其他条款的效力。\n2. 本协议未尽事宜，参照国家相关法律法规及伽House相关规定执行。\n\n如您对本协议有任何疑问，请联系伽House工作人员。\n\n本协议自2025年7月起生效。\n\n伽House`
];


/**
 * Page()函数：注册小程序页面
 *
 * 这是小程序页面的核心函数，类似于：
 * - Vue: export default { ... }
 * - React: class Component extends React.Component { ... }
 * - Angular: @Component({ ... })
 * - WPF: public partial class MainWindow : Window { ... }
 * - Android: public class MainActivity extends AppCompatActivity { ... }
 *
 * Page对象包含：
 * 1. data: 页面数据（响应式）
 * 2. 生命周期函数: onLoad, onShow, onHide等
 * 3. 事件处理函数: 用户交互响应
 * 4. 自定义方法: 业务逻辑处理
 */
Page({
  /**
   * data: 页面数据对象
   *
   * 响应式数据说明：
   * - 类似于Vue的data选项或React的state
   * - 数据变化会自动更新页面显示
   * - 只能通过this.setData()方法修改，不能直接赋值
   * - 支持嵌套对象和数组
   *
   * 数据类型对比：
   * - JavaScript: 动态类型，运行时确定
   * - TypeScript: 静态类型，编译时检查
   * - Java: 强类型，编译时检查
   * - C#: 强类型，编译时检查
   */
  data: {
    // 登录状态标识
    // boolean类型：true表示已登录，false表示未登录
    // 用于控制页面显示不同的内容（登录界面 vs 个人中心界面）
    isLoggedIn: false,

    // 用户信息对象
    // 存储当前登录用户的基本信息
    userInfo: {
      nickName: '',      // 用户昵称，字符串类型，默认为空
      avatarUrl: '',     // 头像URL，字符串类型，默认为空
      role: '学员'       // 用户角色，字符串类型，默认为"学员"
    },

    // 多角色支持相关数据
    // 新版本支持用户拥有多个角色，如既是学员又是讲师
    roles: [],             // 角色数组，如 ['学员', '讲师']，数组类型
    showStudentGroup: false, // 是否显示学员功能区，布尔类型
    showCoachGroup: false,   // 是否显示讲师功能区，布尔类型
    showAdminGroup: false,   // 是否显示管理员功能区，布尔类型
    rolesString: '',         // 角色展示用字符串，如"学员 / 讲师"，用于页面显示

    // 用户协议相关
    agreementChecked: false,  // 用户协议勾选状态，布尔类型，登录前必须勾选

    // 新版用户协议弹窗相关数据
    showAgreementDialog: false, // 是否显示协议弹窗
    agreementTitle: '伽House小程序用户协议', // 弹窗标题
    agreementContent: [], // 协议内容，将由JS动态填充

    // 通知相关数据
    unreadCount: 0  // 未读通知数量，数字类型，用于显示红点和数量
  },

  /**
   * onLoad: 页面生命周期函数 - 页面加载时调用
   *
   * 生命周期对比：
   * - 小程序：onLoad → onShow → onReady → onHide → onUnload
   * - Vue：created → mounted → updated → destroyed
   * - React：constructor → componentDidMount → componentDidUpdate → componentWillUnmount
   * - Android：onCreate → onStart → onResume → onPause → onStop → onDestroy
   * - iOS：viewDidLoad → viewWillAppear → viewDidAppear → viewWillDisappear → viewDidDisappear
   *
   * onLoad特点：
   * 1. 页面首次加载时调用，只调用一次
   * 2. 可以接收页面参数（通过options参数）
   * 3. 适合进行初始化操作，如数据加载、状态设置等
   * 4. 此时页面还未显示，用户看不到内容
   */
  onLoad() {
    // 检查用户登录状态
    // 这是页面加载时的第一个操作，确保页面显示正确的状态
    this.checkLoginStatus();
  },

  onShow() {
    // 设置自定义tabBar高亮“我的”
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      });
    }
    this.checkLoginStatus();

    // 如果用户已登录，获取未读通知数量并更新Badge
    if (this.data.isLoggedIn) {
      this.getUnreadNotificationCount();
    }
  },

  // 头像加载错误处理
  onAvatarError(e) {
    console.log('头像加载失败:', e.detail);
    // 显示默认头像
    const defaultAvatarUrl = 'cloud://cloud1-1gm190n779af8083.636c-cloud1-1gm190n779af8083-1365450081/logo/logo.jpg';
    this.setData({
      'userInfo.avatarUrl': defaultAvatarUrl
    });
  },

  // 头像加载成功处理
  onAvatarLoad(e) {
    console.log('头像加载成功:', e.detail);
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp();
    const isLoggedIn = app.isLoggedIn();
    const userInfo = app.getUserInfo();

    console.log('当前登录状态:', isLoggedIn);
    console.log('用户信息:', userInfo);

    this.setData({
      isLoggedIn: isLoggedIn,
      userInfo: userInfo || {
        nickName: '',
        avatarUrl: '',
        role: '学员'
      }
    });

    // 解析并处理角色信息
    this.processRoles(userInfo || { role: '学员' });

    // 如果已登录，向后台拉取最新用户信息，防止角色已被管理员调整
    if (isLoggedIn) {
      this.refreshUserInfo();
      // 获取未读通知数量
      this.getUnreadNotificationCount();
    }
  },

  // 新增：统一处理角色信息的方法
  processRoles(userInfo) {
    // 优先使用 roles 数组，其次使用 role 字段
    let rawRole = '学员';
    if (userInfo) {
      if (userInfo.roles && userInfo.roles.length) {
        rawRole = userInfo.roles; // 数组
      } else if (userInfo.role) {
        rawRole = userInfo.role; // 字符串
      }
    }

    // 将字符串或数组转成数组形式
    let rolesArray = [];
    if (Array.isArray(rawRole)) {
      rolesArray = rawRole;
    } else if (typeof rawRole === 'string') {
      rolesArray = rawRole.split(/[ ,|/\\]+/).map(r => r.trim()).filter(Boolean);
    }

    if (rolesArray.length === 0) {
      rolesArray = ['学员'];
    }

    // 角色判断
    const isStudent = rolesArray.includes('学员');
    const isCoach = rolesArray.includes('讲师');
    const isAdmin = rolesArray.includes('管理员');

    // 权限组显示逻辑
    const showStudentGroup = isStudent || isCoach || isAdmin; // 所有角色均享有学员功能
    const showCoachGroup = isCoach; // 只有讲师角色才能看到讲师功能
    const showAdminGroup = isAdmin;

    // 更新页面数据
    this.setData({
      roles: rolesArray,
      showStudentGroup,
      showCoachGroup,
      showAdminGroup,
      rolesString: rolesArray.join(' / ')
    });
  },

  // 处理用户信息获取
  async onGetUserInfo(e) {
    console.log('用户信息获取结果:', e.detail);
    
    if (e.detail.userInfo) {
      // 用户同意授权
      console.log('用户同意授权，用户信息:', e.detail.userInfo);
      await this.handleLogin(e.detail.userInfo);
    } else {
      // 用户拒绝授权
      console.log('用户拒绝授权，使用默认信息');
      await this.handleLoginWithDefault();
    }
  },

  // 统一登录处理
  async handleLogin(userInfo = {
    nickName: '微信用户',
    avatarUrl: '',
    gender: 0,
    country: '',
    province: '',
    city: '',
    language: 'zh_CN'
  }) {
    try {
      console.log('开始登录...');
      const app = getApp();
      
      // 获取微信登录凭证
      const loginResult = await wx.login();
      if (!loginResult.code) {
        throw new Error('获取登录凭证失败');
      }

      // 调用云函数进行登录验证
      const cloudResult = await wx.cloud.callFunction({
        name: 'userManagement',
        data: {
          action: 'login',
          data: {
            userInfo: userInfo
          }
        }
      });

      if (cloudResult.result.success) {
        const finalUserInfo = {
          ...userInfo,
          openid: cloudResult.result.openid,
          role: cloudResult.result.role || '学员',
          ...cloudResult.result.userInfo
        };

        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', finalUserInfo);
        app.globalData.userInfo = finalUserInfo;
        app.globalData.isLoggedIn = true;

        // 更新页面数据
        this.setData({
          isLoggedIn: true,
          userInfo: finalUserInfo
        });

        // 解析角色
        this.processRoles(finalUserInfo);

        showToast(this, { message: '登录成功', theme: 'success' });

        console.log('登录成功，用户信息:', finalUserInfo);
        return finalUserInfo;
      } else {
        throw new Error(cloudResult.result.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      showToast(this, { message: '登录失败，请重试', theme: 'error' });
      throw error;
    }
  },

  // 微信一键登录
  async handleWxLogin() {
    if (!this.data.agreementChecked) {
      showToast(this, { message: '请先阅读并同意用户协议', theme: 'warning' });
      return;
    }
    try {
      console.log('开始登录...');
      const app = getApp();
      
      // 先获取微信登录凭证
      const loginResult = await wx.login();
      if (!loginResult.code) {
        throw new Error('获取登录凭证失败');
      }

      // 调用云函数检查用户是否已存在
      const cloudResult = await wx.cloud.callFunction({
        name: 'userManagement',
        data: {
          action: 'checkUser'
        }
      });

      if (cloudResult.result.success) {
        const userInfo = cloudResult.result.userInfo;
        
        if (userInfo && userInfo.nickName) {
          // 老用户，直接登录
          console.log('老用户登录:', userInfo);
          
          // 保存用户信息到本地存储
          wx.setStorageSync('userInfo', userInfo);
          app.globalData.userInfo = userInfo;
          app.globalData.isLoggedIn = true;

          // 更新页面数据
          this.setData({
            isLoggedIn: true,
            userInfo: userInfo
          });

          // 解析角色
          this.processRoles(userInfo);

          showToast(this, { message: '登录成功', theme: 'success' });
        } else {
          // 新用户，跳转到注册页面
          console.log('新用户，跳转到注册页面');
          
          // 准备传递给注册页面的信息
          const registerInfo = {
            openid: cloudResult.result.openid,
            role: '学员'
          };
          
          wx.navigateTo({
            url: `/pages/profile-edit/profile-edit?userInfo=${encodeURIComponent(JSON.stringify(registerInfo))}`
          });
        }
      } else {
        throw new Error(cloudResult.result.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      showToast(this, { message: '登录失败，请重试', theme: 'error' });
    }
  },

  // 获取用户信息 - 使用wx.getUserProfile
  getUserProfile() {
    return new Promise((resolve, reject) => {
      console.log('=== getUserProfile 方法被调用 ===');
      console.log('当前时间:', new Date().toISOString());
      
      // 检查wx.getUserProfile是否存在
      if (typeof wx.getUserProfile !== 'function') {
        console.error('wx.getUserProfile 不存在！');
        reject(new Error('wx.getUserProfile API不存在'));
        return;
      }
      
      console.log('wx.getUserProfile 存在，开始调用...');
      
      wx.getUserProfile({
        desc: '用于完善用户资料',
        lang: 'zh_CN',
        success: (res) => {
          console.log('=== getUserProfile 成功 ===');
          console.log('完整响应:', res);
          console.log('用户信息:', res.userInfo);
          resolve(res.userInfo);
        },
        fail: (err) => {
          console.error('=== getUserProfile 失败 ===');
          console.error('错误对象:', err);
          console.error('错误消息:', err.errMsg);
          console.error('错误代码:', err.errCode);
          reject(new Error('获取用户信息失败: ' + (err.errMsg || '未知错误')));
        }
      });
      
      console.log('wx.getUserProfile 调用完成');
    });
  },

  // 用户点击头像选择新头像
  async onChooseAvatar(e) {
    console.log('=== profile页面头像选择开始 ===');
    console.log('选择头像事件详情:', e);

    const { avatarUrl } = e.detail;
    console.log('获取到的头像URL:', avatarUrl);
    console.log('头像URL类型:', typeof avatarUrl);
    console.log('头像URL长度:', avatarUrl ? avatarUrl.length : 0);

    if (!avatarUrl) {
      console.error('未获取到头像URL');
      showToast(this, { message: '未获取到头像', theme: 'error' });
      return;
    }

    showLoading(this, '上传头像中...');
    try {
      // 上传头像到云存储
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 9);
      const cloudPath = `avatars/${timestamp}_${randomStr}.jpg`;

      console.log('开始上传头像到云存储:', cloudPath);
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath,
        filePath: avatarUrl
      });

      console.log('云存储上传结果:', uploadResult);

      // 获取公开访问的URL
      const publicUrl = uploadResult.fileID;
      console.log('获取到的公开URL:', publicUrl);

      // 调用云函数更新头像
      console.log('调用云函数 updateAvatar...');
      const res = await wx.cloud.callFunction({
        name: 'userManagement',
        data: {
          action: 'updateAvatar',
          data: { avatarUrl: publicUrl }
        }
      });

      console.log('云函数调用结果:', res);
      console.log('云函数返回详情:', JSON.stringify(res.result, null, 2));

      if (res.result && res.result.success) {
        console.log('云函数调用成功，开始更新本地数据...');

        // 更新本地 userInfo
        const app = getApp();
        const userInfo = { ...this.data.userInfo, avatarUrl: publicUrl };
        console.log('更新后的用户信息:', userInfo);

        this.setData({ 'userInfo.avatarUrl': publicUrl });
        wx.setStorageSync('userInfo', userInfo);
        app.globalData.userInfo = userInfo;

        console.log('本地数据更新完成');
        showToast(this, { message: '头像已更新', theme: 'success' });
      } else {
        console.error('云函数调用失败:', res.result);
        showToast(this, { message: res.result.message || '头像更新失败', theme: 'error' });
      }
    } catch (err) {
      console.error('=== 头像上传失败 ===');
      console.error('错误详情:', err);
      console.error('错误堆栈:', err.stack);
      console.error('错误消息:', err.message);
      showToast(this, { message: '头像上传失败', theme: 'error' });
    } finally {
      hideToast(this);
      console.log('=== profile页面头像选择结束 ===');
    }
  },

  // 退出登录
  logout() {
    console.log('退出登录被调用');
    console.log('当前用户信息:', this.data.userInfo);
    console.log('当前登录状态:', this.data.isLoggedIn);
    
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        console.log('退出确认结果:', res);
        if (res.confirm) {
          console.log('用户确认退出');
          this.performLogout();
        } else {
          console.log('用户取消退出');
        }
      },
      fail: (err) => {
        console.error('退出确认失败:', err);
      }
    });
  },

  // 执行退出登录
  performLogout() {
    try {
      console.log('开始执行退出登录...');
      const app = getApp();
      
      // 调用app的logout方法
      app.logout();
      
      // 更新页面数据
      this.setData({
        isLoggedIn: false,
        userInfo: {
          nickName: '',
          avatarUrl: '',
          role: '学员'
        }
      });

      // 重置角色信息
      this.processRoles({ role: '学员' });
      
      console.log('退出登录完成，当前状态:', this.data.isLoggedIn);
      console.log('退出登录完成，当前用户信息:', this.data.userInfo);
      
      // 验证退出是否成功
      const storedUserInfo = wx.getStorageSync('userInfo');
      console.log('本地存储中的用户信息:', storedUserInfo);
      
      showToast(this, { message: '已退出登录', theme: 'success' });
      
      // 延迟检查状态，确保退出成功
      setTimeout(() => {
        console.log('延迟检查 - 登录状态:', this.data.isLoggedIn);
        console.log('延迟检查 - 全局登录状态:', app.isLoggedIn());
      }, 1000);
      
    } catch (error) {
      console.error('退出登录过程中出错:', error);
      showToast(this, { message: '退出失败，请重试', theme: 'error' });
    }
  },

  // 学员功能
  goToMyBookings() {
    wx.navigateTo({
      url: '/pages/my-bookings/my-bookings'
    });
  },

  goToHistory() {
    wx.navigateTo({
      url: '/pages/my-bookings/my-bookings?tab=history'
    });
  },

  goToProfile() {
    wx.navigateTo({
      url: '/pages/profile-edit/profile-edit?editProfile=1'
    });
  },

  // 讲师功能
  goToMySchedule() {
    showToast(this, { message: '功能开发中', theme: 'warning' });
  },

  goToCoachSchedule() {
    wx.navigateTo({
      url: '/pages/coach-schedule/coach-schedule'
    });
  },

  goToStudents() {
    showToast(this, { message: '功能开发中', theme: 'warning' });
  },

  // 管理员功能
  goToUserManagement() {
    wx.navigateTo({
      url: '/pages/user-management/user-management'
    });
  },

  goToCourseManagement() {
    wx.navigateTo({
      url: '/pages/course-management/course-management'
    });
  },

  goToBookingOverview() {
    showToast(this, { message: '功能开发中', theme: 'warning' });
  },

  goToMembershipCard() {
    wx.navigateTo({
      url: '/pages/membership-card/membership-card'
    });
  },

  goToMembershipCardManagement() {
    wx.navigateTo({
      url: '/pages/membership-card-management/membership-card-management'
    });
  },

  // 跳转到关于页面
  goToAbout() {
    wx.showModal({
      title: '关于伽House约课小程序',
      content: '版本：1.0.0\n  运营方：伽House\n  如有问题请联系伽House工作人员。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 跳转到系统设置页面
  goToSystemSettings() {
    wx.navigateTo({
      url: '/pages/system-settings/system-settings'
    });
  },

  onAgreementChange(e) {
    this.setData({ agreementChecked: e.detail.checked });
  },
  """  showAgreement() {
    this.setData({
      showAgreementDialog: true, // 显示弹窗
      agreementTitle: '伽House小程序用户协议', // 设置标题
      // 将协议段落处理成适合渲染的格式
      agreementContent: agreementParagraphs.map(p => {
        // 进一步按换行符拆分，用于渲染标题和正文
        const parts = p.split('
');
        return {
          title: parts[0], // 第一行作为标题
          body: parts.slice(1).join('
') // 剩余部分作为正文
        };
      })
    });
  },

  /**
   * 隐藏用户协议弹窗
   *
   * 功能说明：
   * 1. 用户点击弹窗的“我已知晓”按钮或遮罩层时调用
   * 2. 将 `showAgreementDialog` 设置为 false，隐藏弹窗
   */
  hideAgreementDialog() {
    this.setData({
      showAgreementDialog: false
    });
  },""

  // 新增：刷新用户信息，确保角色始终是最新
  async refreshUserInfo() {
    try {
      console.log('刷新用户信息...');
      const app = getApp();

      // 获取临时登录凭证
      const loginResult = await wx.login();
      if (!loginResult.code) {
        throw new Error('获取登录凭证失败');
      }

      // 调用云函数，仅查询不创建
      const cloudResult = await wx.cloud.callFunction({
        name: 'userManagement',
        data: {
          action: 'checkUser'
        }
      });

      if (cloudResult.result.success && cloudResult.result.userInfo) {
        const latestUserInfo = cloudResult.result.userInfo;
        console.log('最新用户信息:', latestUserInfo);

        // 更新缓存及全局
        wx.setStorageSync('userInfo', latestUserInfo);
        app.globalData.userInfo = latestUserInfo;

        // 更新页面
        this.setData({
          userInfo: latestUserInfo
        });

        // 重新解析角色
        this.processRoles(latestUserInfo);
      } else {
        console.log('未获取到用户信息或云函数返回失败');
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error);
    }
  },

  onAdminAlbumTap() {
    // 跳转到相册管理页面
    wx.navigateTo({
      url: '/pages/album-management/album-management'
    });
  },

  /**
   * 获取未读通知数量
   *
   * 功能说明：
   * 1. 调用通知管理云函数获取当前用户的未读通知数量
   * 2. 更新页面数据中的unreadCount字段
   * 3. 用于在通知图标上显示红点和数量
   *
   * 调用时机：
   * - 页面显示时（onShow）
   * - 用户登录成功后
   * - 从通知页面返回时
   */
  async getUnreadNotificationCount() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.openid) {
        return;
      }

      const result = await wx.cloud.callFunction({
        name: 'notificationManagement',
        data: {
          action: 'getUnreadCount',
          data: {
            userId: userInfo.openid
          }
        }
      });

      if (result.result.success) {
        const unreadCount = result.result.data.unreadCount || 0;
        this.setData({
          unreadCount
        });

        // 更新tabBar的Badge状态
        if (typeof this.getTabBar === 'function' && this.getTabBar()) {
          this.getTabBar().setProfileBadge(unreadCount > 0);
        }
      }
    } catch (error) {
      console.error('获取未读通知数量失败:', error);
      // 获取失败时不显示错误提示，避免影响用户体验
    }
  },

  /**
   * 跳转到通知列表页面
   *
   * 功能说明：
   * 1. 点击通知图标时触发
   * 2. 跳转到通知列表页面
   * 3. 用户可以查看所有通知消息
   *
   * 事件绑定：
   * 在WXML中通过bind:tap="goToNotifications"绑定
   */
  goToNotifications() {
    console.log('跳转到通知列表页面');
    wx.navigateTo({
      url: '/pages/notifications/notifications'
    });
  }
});