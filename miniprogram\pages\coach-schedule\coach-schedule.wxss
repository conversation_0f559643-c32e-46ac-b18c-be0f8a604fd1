/* coach-schedule.wxss */
.container {
  padding: 16px;
  padding-bottom: calc(16px + 120rpx + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
  /* 使用flexbox布局，让内容充分利用空间 */
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* 视图切换栏样式 */
.view-section {
  width: 100%;
  padding-bottom:0px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0; /* 防止视图切换区域被压缩 */
}

/* 筛选栏样式 */
.filter-section {
  width: 100%;
  padding-bottom:0px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 预约标签页样式 - 与my-bookings页面保持一致 */
.booking-tabs {
  width: 100%;
  display: flex;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
  margin: 0 4px; /* 添加左右边距，避免按钮贴边 */
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.booking-tab {
  flex: 1;
  text-align: center;
  padding: 8px 12px; /* 增加左右内边距 */
  font-size: 14px;
  border-radius: 6px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.booking-tab.active {
  background-color: #0052d9;
  color: white;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

/* 日期选择器样式 */
.date-section {
  margin-bottom: 12px;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.date-tabs-scroll {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
  overflow-x: auto;
  padding: 4px 0 6px 0;
  background: #fff;
  border-radius: 8px;
  margin: 4px 0;
  /* 确保滑动流畅 */
  -webkit-overflow-scrolling: touch;
  flex-shrink: 0; /* 防止日期选择器被压缩 */
}
.date-tab {
  display: inline-block;
  width: 50px;
  text-align: center;
  margin-right: 8px;
  padding: 6px 0 0 0;
  cursor: pointer;
  min-height: 38px;
  /* 防止收缩 */
  flex-shrink: 0;
}
.date-tab:last-child {
  margin-right: 0;
}
.date-tab .tab-label {
  display: block;
  height: 18px;
  line-height: 18px;
}
.date-tab .tab-date {
  display: block;
  font-size: 12px;
  margin-top: 2px;
  height: 16px;
  line-height: 16px;
  color: #bbb;
}

.course-list {
  width: 100%;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  /* 移除margin-bottom，让容器贴紧底部 */
  flex: 1;
  display: flex;
  flex-direction: column;
}
.course-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 14px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.course-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
.course-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  flex-shrink: 0;
  white-space: nowrap;
}
.course-status.available {
  background-color: #e8f5e8;
  color: #52c41a;
}
.course-status.booked {
  background-color: #e6f3ff;
  color: #0052d9;
}
.course-status.full {
  background-color: #fff2e8;
  color: #fa8c16;
}
.course-status.ended {
  background-color: #f0f0f0;
  color: #888;
}
.course-info-list {
  margin-bottom: 12px;
}
.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
.info-item:last-child {
  margin-bottom: 0;
}
.info-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

/* 活动详情单行显示并截断 */
.activity-detail {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
.course-footer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
  font-family: "PingFang SC", "PingFang", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

.tag-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 已预约学员区域样式 */
.booked-students-section {
  margin: 12px 0;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.collapse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f8f8;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.collapse-content {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.student-list {
  padding: 8px 0;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  font-size: 14px;
  color: #666;
}

.student-item t-icon {
  margin-right: 8px;
  color: #0052d9;
}

.student-name {
  flex: 1;
}

.no-students {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 20px 0;
}
#tab-bar-placeholder {
  height: 120rpx;
}
.timeline-date {
  color: #888;
  font-size: 26rpx;
  text-align: left;
  margin: 32rpx 0 12rpx 16rpx;
  font-weight: bold;
}
.end-indicator {
  text-align: center;
  color: #bbb;
  font-size: 24rpx;
  margin: 24rpx 0 12rpx 0;
}

.loading-indicator {
  text-align: center;
  color: #0052d9;
  font-size: 24rpx;
  margin: 24rpx 0 12rpx 0;
  padding: 20rpx 0;
}