// miniprogram/pages/membership-card/membership-card.js
// 功能：展示用户的考勤卡信息，包括有效期、剩余次数等
// 支持多卡展示和智能排序

import { showToast, showLoading, hideToast } from '../../utils/toast.js';
import {
  getAllMembershipCards,
  calculateCardStatus,
  formatDate,
  getCardStatistics
} from '../../utils/membershipCardUtils.js';

Page({
  data: {
    cards: [],           // 会员卡列表
    loading: true,       // 加载状态
    statistics: {        // 会员卡统计信息
      total: 0,
      valid: 0,
      expired: 0,
      exhausted: 0,
      totalRemainingTimes: 0,
      expiringSoon: 0
    }
  },
  onLoad() {
    showLoading(this, '加载中...');
    this.fetchMembershipCards({ showLoading: true });
  },
  
  onShow() {
    // 每次显示页面时自动刷新数据
    this.fetchMembershipCards({ showLoading: false });
  },
  onPullDownRefresh() {
    this.fetchMembershipCards({ showLoading: false }, () => wx.stopPullDownRefresh());
  },
  /**
   * 获取考勤卡数据
   * 从云数据库获取当前用户的考勤卡信息，支持多卡展示和智能排序
   */
  async fetchMembershipCards({ showLoading }, callback) {
    if (showLoading) this.setData({ loading: true });
    try {
      const app = getApp();
      const userInfo = app.getUserInfo();
      if (!userInfo || !userInfo.openid) {
        showToast(this, { message: '请先登录', theme: 'warning' });
        this.setData({ cards: [], statistics: {} });
        if (showLoading) this.setData({ loading: false });
        if (callback) callback();
        return;
      }

      const db = wx.cloud.database();

      // 使用工具函数获取所有会员卡（已排序）
      const allCards = await getAllMembershipCards(userInfo.openid, db);

      // 格式化卡片数据用于显示
      const formattedCards = allCards.map(card => {
        const cardStatus = calculateCardStatus(card);

        return {
          ...card,
          validFrom: formatDate(card.validFrom),
          validTo: formatDate(card.validTo),
          issueDate: formatDate(card.issueDate),
          isExpired: cardStatus.isExpired,
          isExpiring: cardStatus.isExpiring,
          isValid: cardStatus.isValid,
          displayStatus: cardStatus.status
        };
      });

      // 计算统计信息
      const statistics = getCardStatistics(allCards);

      this.setData({
        cards: formattedCards,
        statistics: statistics
      });

      console.log('会员卡数据加载完成:', {
        总数: statistics.total,
        有效: statistics.valid,
        剩余总次数: statistics.totalRemainingTimes,
        即将到期: statistics.expiringSoon
      });

    } catch (e) {
      console.error('获取考勤卡失败:', e);
      showToast(this, { message: '获取考勤卡失败', theme: 'error' });
      this.setData({ cards: [], statistics: {} });
    }
    if (showLoading) this.setData({ loading: false });
    hideToast(this);
    if (callback) callback();
  },
  formatDate(dateStr) {
    if (!dateStr) return '';
    const d = new Date(dateStr);
    if (isNaN(d.getTime())) return '';
    return `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}-${d.getDate().toString().padStart(2,'0')}`;
  }
}); 