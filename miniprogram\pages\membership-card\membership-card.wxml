<!--
  membership-card.wxml - 考勤卡页面结构文件

  页面功能：展示用户的考勤卡信息，支持多卡展示
  设计风格：现代化卡片设计，注重视觉层次和用户体验
-->
<view class="membership-card-page" data-loading="{{loading}}">
  <!-- 统计信息卡片 - 当有数据时显示 -->
  <view class="statistics-card" wx:if="{{!loading && cards.length > 0}}">
    <view class="stats-row">
      <view class="stat-item">
        <text class="stat-number">{{statistics.total}}</text>
        <text class="stat-label">总卡数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.valid}}</text>
        <text class="stat-label">有效卡</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.totalRemainingTimes}}</text>
        <text class="stat-label">剩余次数</text>
      </view>
      <view class="stat-item" wx:if="{{statistics.expiringSoon > 0}}">
        <text class="stat-number warning">{{statistics.expiringSoon}}</text>
        <text class="stat-label">即将到期</text>
      </view>
    </view>
  </view>

  <!-- 考勤卡列表容器 - 当有数据时显示 -->
  <block wx:if="{{!loading && cards.length > 0}}">
    <view class="card-list">
      <!--
        考勤卡片循环渲染
        wx:for: 遍历cards数组，每个元素赋值给item
        wx:key: 使用_id作为唯一标识，提升渲染性能
      -->
      <view class="membership-card" wx:for="{{cards}}" wx:key="_id" data-index="{{index}}">
        <!-- 卡片头部区域 -->
        <view class="card-header">
          <view style="display: flex; align-items: center;">
            <t-icon name="creditcard" size="24" />
            <view class="card-number">{{item.cardNumber}}</view>
          </view>

          <!-- 状态标签 - 根据卡片状态显示不同的标签 -->
          <t-tag wx:if="{{item.isExpired}}" theme="danger" size="small">已过期</t-tag>
          <t-tag wx:elif="{{item.displayStatus === '已用完'}}" theme="default" size="small">已用完</t-tag>
          <t-tag wx:elif="{{item.displayStatus === '已冻结'}}" theme="default" size="small">已冻结</t-tag>
          <t-tag wx:elif="{{item.isExpiring}}" theme="warning" size="small">即将到期</t-tag>
          <t-tag wx:elif="{{item.isValid}}" theme="success" size="small">有效</t-tag>
          <t-tag wx:else theme="default" size="small">{{item.displayStatus}}</t-tag>
        </view>

        <!--
          卡片主体内容区域
          包含：有效期、总次数、剩余次数、颁发日期
        -->
        <view class="card-body">
          <!--
            信息行 - 有效期
            使用flex布局，左侧标签，右侧数值
          -->
          <view class="card-row validity-row">
            <text class="label">有效期</text>
            <text class="value">{{item.validFrom}} ~ {{item.validTo}}</text>
          </view>

          <!-- 信息行 - 总次数 -->
          <view class="card-row total-times-row">
            <text class="label">总次数</text>
            <text class="value">{{item.totalTimes}}</text>
          </view>

          <!-- 信息行 - 剩余次数（重要信息，突出显示） -->
          <view class="card-row remaining-times-row">
            <text class="label">剩余次数</text>
            <text class="value highlight">{{item.remainingTimes}}</text>
          </view>

          <!-- 信息行 - 颁发日期 -->
          <view class="card-row issue-date-row">
            <text class="label">颁发日期</text>
            <text class="value">{{item.issueDate}}</text>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!--
    空状态组件 - 当没有考勤卡时显示
    wx:if: 条件渲染，当不在加载且卡片数量为0时显示
    description: 空状态描述文字
  -->
  <t-empty wx:if="{{!loading && cards.length === 0}}"
           description="暂无考勤卡"
           icon="creditcard" />

  <!-- TabBar占位符 - 预留底部导航栏空间 -->
  <view id="tab-bar-placeholder"></view>

  <!-- Toast消息提示组件 -->
  <t-toast id="t-toast" />
</view>